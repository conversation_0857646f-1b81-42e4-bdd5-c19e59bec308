<?php

namespace App\Models;

use CodeIgniter\Model;

/**
 * Model for the 'dakoii_users' table, representing super-admin users for the Dakoii Portal.
 * Includes business identifiers, profile, security, audit, and lifecycle fields.
 */
class DakoiiUserModel extends Model
{
    /**
     * @var string Database connection group.
     */
    protected $DBGroup          = 'default';

    /**
     * @var string The database table this model represents.
     */
    protected $table            = 'dakoii_users';

    /**
     * @var string The primary key for the table.
     */
    protected $primaryKey       = 'id';

    /**
     * @var bool Whether the model should use auto-incrementing primary keys.
     */
    protected $useAutoIncrement = true;

    /**
     * @var string The type of result to return. 'array' or 'object'.
     */
    protected $returnType       = 'array';

    /**
     * @var bool Whether the model should use soft deletes (deleted_at column).
     */
    protected $useSoftDeletes   = true;

    /**
     * @var bool Whether to protect fields from mass assignment.
     */
    protected $protectFields    = true;

    /**
     * @var array The fields that can be mass-assigned.
     * Includes audit-by columns and all business, profile, and security fields.
     */
    protected $allowedFields    = [
        // Business identifiers
        'user_code', 'username', 'email',
        // Profile
        'name', 'role', 'id_photo_path',
        // Security & lifecycle
        'password_hash', 'activation_token', 'is_activated', 'password_reset_token', 'last_login_at',
        // Audit-by columns
        'created_by', 'updated_by', 'deleted_by',
    ];

    /**
     * @var bool Whether the model should use timestamps.
     */
    protected $useTimestamps = true;

    /**
     * @var string The name of the `created_at` column.
     */
    protected $createdField  = 'created_at';

    /**
     * @var string The name of the `updated_at` column.
     */
    protected $updatedField  = 'updated_at';

    /**
     * @var string The name of the `deleted_at` column.
     */
    protected $deletedField  = 'deleted_at';

    /**
     * @var array Validation rules for the model's fields.
     * Enforces uniqueness, required fields, and correct formats.
     * Note: For updates, uniqueness rules should be handled in the controller
     * to properly exclude the current record.
     */
    protected $validationRules = [
        'user_code'    => 'permit_empty',
        'username'     => 'permit_empty',
        'email'        => 'permit_empty|valid_email',
        'name'         => 'permit_empty',
        'role'         => 'permit_empty',
        'password_hash'=> 'permit_empty',
    ];

    /**
     * @var array Custom error messages for validation rules.
     */
    protected $validationMessages   = [];

    /**
     * @var bool Whether to skip validation on insert/update.
     */
    protected $skipValidation       = false;

    /**
     * @var bool Whether to clean validation rules before validation.
     */
    protected $cleanValidationRules = true;

    /**
     * @var bool Whether to allow callbacks.
     */
    protected $allowCallbacks = true;
    protected $beforeInsert   = [];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];
}