<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($title) ? $title : 'Dakoii Admin Portal' ?></title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    <link rel="icon" type="image/x-icon" href="<?= base_url('public/system_images/favicon.ico') ?>">
    <style>
        :root {
            /* Color Palette */
            --bg-primary: #0A0E27;
            --bg-secondary: #151B3C;
            --bg-tertiary: #1E2749;
            --surface-card: rgba(30, 39, 73, 0.6);
            --surface-card-hover: rgba(30, 39, 73, 0.8);
            --glass-bg: rgba(255, 255, 255, 0.05);
            --glass-border: rgba(255, 255, 255, 0.1);
            
            /* Gradients */
            --gradient-primary: linear-gradient(135deg, #FF006E, #8338EC);
            --gradient-secondary: linear-gradient(45deg, #06FFA5, #00D4FF);
            --gradient-accent: linear-gradient(90deg, #FFB700, #FF006E);
            
            /* Text Colors */
            --text-primary: #FFFFFF;
            --text-secondary: #B8BCC8;
            --text-tertiary: #7C8091;
            --text-muted: #4A4E5C;
            
            /* Typography */
            --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            --font-mono: 'JetBrains Mono', 'Fira Code', monospace;
            
            /* Spacing */
            --spacing-xs: 0.25rem;
            --spacing-sm: 0.5rem;
            --spacing-md: 1rem;
            --spacing-lg: 1.5rem;
            --spacing-xl: 2rem;
            --spacing-2xl: 3rem;
            --spacing-3xl: 4rem;
            
            /* Border Radius */
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
            --radius-2xl: 1.5rem;
            
            /* Shadows */
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            --shadow-glow-primary: 0 0 20px rgba(255, 0, 110, 0.3);
            --shadow-glow-secondary: 0 0 20px rgba(0, 212, 255, 0.3);
            
            /* Layout */
            --sidebar-width: 280px;
            --sidebar-collapsed-width: 80px;
            --header-height: 70px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-primary);
            background: var(--bg-primary);
            color: var(--text-primary);
            overflow-x: hidden;
            position: relative;
        }

        /* Background Pattern */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 50%, rgba(255, 0, 110, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(131, 56, 236, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 40% 80%, rgba(0, 212, 255, 0.05) 0%, transparent 50%);
            z-index: -1;
        }

        /* Layout Structure */
        .admin-layout {
            display: flex;
            min-height: 100vh;
        }

        /* Sidebar */
        .sidebar {
            width: var(--sidebar-width);
            background: rgba(20, 27, 45, 0.8);
            backdrop-filter: blur(10px);
            border-right: 1px solid var(--glass-border);
            position: fixed;
            height: 100vh;
            left: 0;
            top: 0;
            z-index: 1000;
            transition: all 0.3s ease;
            overflow-y: auto;
        }

        .sidebar.collapsed {
            width: var(--sidebar-collapsed-width);
        }

        .sidebar-header {
            padding: var(--spacing-xl);
            border-bottom: 1px solid var(--glass-border);
            text-align: center;
        }

        .sidebar.collapsed .sidebar-header {
            padding: var(--spacing-lg) var(--spacing-md);
        }

        .sidebar-logo {
            font-size: 1.5rem;
            font-weight: 700;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            transition: all 0.3s ease;
        }

        .sidebar.collapsed .sidebar-logo {
            font-size: 1rem;
        }

        .sidebar-nav {
            padding: var(--spacing-lg);
        }

        .nav-item {
            margin-bottom: var(--spacing-sm);
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: var(--spacing-md);
            color: var(--text-secondary);
            text-decoration: none;
            border-radius: var(--radius-md);
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-link:hover {
            background: var(--glass-bg);
            color: var(--text-primary);
        }

        .nav-link.active {
            background: var(--gradient-primary);
            color: var(--text-primary);
        }

        .nav-icon {
            width: 20px;
            height: 20px;
            margin-right: var(--spacing-md);
            flex-shrink: 0;
        }

        .sidebar.collapsed .nav-text {
            display: none;
        }

        .sidebar.collapsed .nav-icon {
            margin-right: 0;
        }

        /* Main Content */
        .main-content {
            flex: 1;
            margin-left: var(--sidebar-width);
            transition: all 0.3s ease;
        }

        .sidebar.collapsed + .main-content {
            margin-left: var(--sidebar-collapsed-width);
        }

        /* Header */
        .header {
            height: var(--header-height);
            background: var(--surface-card);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid var(--glass-border);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 var(--spacing-xl);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-left {
            display: flex;
            align-items: center;
        }

        .sidebar-toggle {
            background: none;
            border: none;
            color: var(--text-secondary);
            font-size: 1.25rem;
            cursor: pointer;
            padding: var(--spacing-sm);
            border-radius: var(--radius-sm);
            transition: all 0.3s ease;
            margin-right: var(--spacing-lg);
        }

        .sidebar-toggle:hover {
            background: var(--glass-bg);
            color: var(--text-primary);
        }

        .page-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }

        .user-menu {
            position: relative;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--gradient-secondary);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .user-avatar:hover {
            box-shadow: var(--shadow-glow-secondary);
        }

        /* Content Area */
        .content {
            padding: var(--spacing-xl);
            min-height: calc(100vh - var(--header-height));
        }

        /* Cards */
        .card {
            background: var(--surface-card);
            backdrop-filter: blur(10px);
            border: 1px solid var(--glass-border);
            border-radius: var(--radius-xl);
            padding: var(--spacing-xl);
            transition: all 0.3s ease;
            margin-bottom: var(--spacing-xl);
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl);
        }

        .card-header {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--text-secondary);
            margin-bottom: var(--spacing-lg);
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        /* Buttons */
        .btn {
            display: inline-block;
            padding: var(--spacing-md) var(--spacing-xl);
            border: none;
            border-radius: var(--radius-md);
            font-size: 0.875rem;
            font-weight: 600;
            text-decoration: none;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-primary {
            background: var(--gradient-primary);
            color: var(--text-primary);
        }

        .btn-secondary {
            background: var(--glass-bg);
            color: var(--text-secondary);
            border: 1px solid var(--glass-border);
        }

        .btn:hover {
            transform: translateY(-1px);
        }

        .btn-primary:hover {
            box-shadow: var(--shadow-glow-primary);
        }

        /* Form Elements */
        .form-group {
            margin-bottom: var(--spacing-lg);
        }

        .form-label {
            display: block;
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--text-secondary);
            margin-bottom: var(--spacing-sm);
        }

        .form-input {
            width: 100%;
            padding: var(--spacing-md);
            background: var(--glass-bg);
            border: 1px solid var(--glass-border);
            border-radius: var(--radius-md);
            color: var(--text-primary);
            font-size: 0.875rem;
            transition: all 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #00D4FF;
            box-shadow: 0 0 0 2px rgba(0, 212, 255, 0.2);
        }

        /* Select dropdown styling */
        .form-input select,
        select.form-input {
            background: var(--glass-bg);
            color: var(--text-primary);
            cursor: pointer;
        }

        /* Dropdown options styling - Force dark theme for options */
        .form-input option,
        select.form-input option {
            background: #1a1a2e !important;
            color: #ffffff !important;
            padding: 8px 12px !important;
            border: none !important;
        }

        .form-input option:hover,
        select.form-input option:hover {
            background: #16213e !important;
            color: #ffffff !important;
        }

        .form-input option:checked,
        select.form-input option:checked,
        .form-input option:selected,
        select.form-input option:selected {
            background: #00D4FF !important;
            color: #1a1a2e !important;
            font-weight: 600 !important;
        }

        /* Additional select styling for better visibility */
        select.form-input {
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ffffff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 12px center;
            background-size: 16px;
            padding-right: 40px;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .sidebar.mobile-open {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .content {
                padding: var(--spacing-md);
            }
        }

        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .fade-in {
            animation: fadeIn 0.4s ease-out;
        }

        /* Additional Form Styles */
        .current-info {
            display: flex;
            align-items: center;
            gap: var(--spacing-lg);
            padding: var(--spacing-lg);
            background: rgba(0, 0, 0, 0.2);
            border-radius: var(--radius-md);
            margin-bottom: var(--spacing-xl);
            border: 1px solid var(--glass-border);
        }

        .current-avatar img,
        .avatar-placeholder {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            object-fit: cover;
        }

        .avatar-placeholder {
            background: var(--gradient-secondary);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
        }

        .current-details h3 {
            margin: 0 0 var(--spacing-sm) 0;
            color: var(--text-primary);
            font-size: 1.5rem;
        }

        .current-details p {
            margin: 0 0 var(--spacing-xs) 0;
            color: var(--text-secondary);
        }

        .role-badge {
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--radius-sm);
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .role-admin { background: var(--gradient-primary); color: var(--text-primary); }
        .role-moderator { background: var(--gradient-accent); color: var(--text-primary); }
        .role-user { background: var(--glass-bg); color: var(--text-secondary); border: 1px solid var(--glass-border); }

        .form-section {
            margin-bottom: var(--spacing-xl);
            padding-bottom: var(--spacing-lg);
            border-bottom: 1px solid var(--glass-border);
        }

        .form-section:last-of-type {
            border-bottom: none;
            padding-bottom: 0;
        }

        .role-permissions {
            margin-top: var(--spacing-md);
        }

        .permission-preview {
            padding: var(--spacing-md);
            background: rgba(0, 0, 0, 0.2);
            border-radius: var(--radius-md);
            border: 1px solid var(--glass-border);
        }

        .permission-preview h4 {
            margin: 0 0 var(--spacing-sm) 0;
            color: var(--text-primary);
            font-size: 1rem;
        }

        .permission-preview ul {
            margin: 0;
            padding-left: var(--spacing-md);
            color: var(--text-secondary);
        }

        .permission-preview li {
            margin-bottom: var(--spacing-xs);
            font-size: 0.875rem;
        }

        .photo-upload {
            display: flex;
            gap: var(--spacing-lg);
            align-items: flex-start;
        }

        .current-photo img {
            width: 120px;
            height: 120px;
            border-radius: var(--radius-md);
            object-fit: cover;
            border: 2px solid var(--glass-border);
        }

        .no-photo {
            width: 120px;
            height: 120px;
            border-radius: var(--radius-md);
            background: var(--glass-bg);
            border: 2px dashed var(--glass-border);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-tertiary);
            font-size: 0.875rem;
        }

        .upload-controls {
            flex: 1;
        }

        .file-input {
            display: none;
        }

        .file-label {
            display: inline-block;
            padding: var(--spacing-md) var(--spacing-lg);
            background: var(--glass-bg);
            border: 1px solid var(--glass-border);
            border-radius: var(--radius-md);
            color: var(--text-secondary);
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.875rem;
            font-weight: 600;
        }

        .file-label:hover {
            background: var(--surface-card-hover);
            color: var(--text-primary);
        }

        .security-notice {
            display: flex;
            gap: var(--spacing-md);
            padding: var(--spacing-lg);
            background: rgba(0, 212, 255, 0.1);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: var(--radius-md);
            margin: var(--spacing-lg) 0;
        }

        .notice-icon {
            font-size: 1.5rem;
            flex-shrink: 0;
        }

        .notice-content h4 {
            margin: 0 0 var(--spacing-sm) 0;
            color: var(--text-primary);
            font-size: 1rem;
        }

        .notice-content ul {
            margin: 0;
            padding-left: var(--spacing-md);
            color: var(--text-secondary);
        }

        .notice-content li {
            margin-bottom: var(--spacing-xs);
            font-size: 0.875rem;
        }

        .form-actions {
            display: flex;
            gap: var(--spacing-md);
            justify-content: flex-end;
            padding-top: var(--spacing-lg);
            border-top: 1px solid var(--glass-border);
        }

        .info-card {
            background: var(--surface-card);
            backdrop-filter: blur(10px);
            border: 1px solid var(--glass-border);
            border-radius: var(--radius-xl);
            padding: var(--spacing-xl);
            height: fit-content;
        }

        .glass-effect {
            background: rgba(255, 255, 255, 0.05);
        }

        .info-header h3 {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-primary);
            font-size: 1.2rem;
        }

        .permission-level {
            padding: var(--spacing-md);
            border-radius: var(--radius-md);
            margin-bottom: var(--spacing-md);
        }

        .permission-level.admin {
            background: rgba(255, 0, 110, 0.1);
            border: 1px solid rgba(255, 0, 110, 0.3);
        }

        .permission-level.moderator {
            background: rgba(255, 183, 0, 0.1);
            border: 1px solid rgba(255, 183, 0, 0.3);
        }

        .permission-level.user {
            background: rgba(0, 212, 255, 0.1);
            border: 1px solid rgba(0, 212, 255, 0.3);
        }

        .permission-level h4 {
            margin: 0 0 var(--spacing-xs) 0;
            color: var(--text-primary);
            font-size: 1rem;
        }

        .permission-level p {
            margin: 0;
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        .edit-restrictions h4 {
            margin: 0 0 var(--spacing-sm) 0;
            color: var(--text-primary);
            font-size: 1rem;
        }

        .edit-restrictions ul {
            margin: 0;
            padding-left: var(--spacing-md);
            color: var(--text-secondary);
        }

        .edit-restrictions li {
            margin-bottom: var(--spacing-xs);
            font-size: 0.875rem;
        }
    </style>
    <?= isset($additional_css) ? $additional_css : '' ?>
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">DAKOII</div>
            </div>
            <nav class="sidebar-nav">
                <!-- Standard Sidebar Navigation -->
                <div class="nav-item">
                    <a href="<?= base_url('dakoii/dashboard') ?>" class="nav-link <?= (current_url() == base_url('dakoii/dashboard')) ? 'active' : '' ?>">
                        <span class="nav-icon">📊</span>
                        <span class="nav-text">Dashboard</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="<?= base_url('dakoii/organizations') ?>" class="nav-link <?= (strpos(current_url(), 'dakoii/organizations') !== false) ? 'active' : '' ?>">
                        <span class="nav-icon">🏢</span>
                        <span class="nav-text">Organizations</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="<?= base_url('dakoii/users') ?>" class="nav-link <?= (strpos(current_url(), 'dakoii/users') !== false) ? 'active' : '' ?>">
                        <span class="nav-icon">👥</span>
                        <span class="nav-text">System Users</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="<?= base_url('dakoii/government') ?>" class="nav-link <?= (strpos(current_url(), 'dakoii/government') !== false) ? 'active' : '' ?>">
                        <span class="nav-icon">🏛️</span>
                        <span class="nav-text">Government Structure</span>
                    </a>
                </div>
                <div class="nav-item" style="margin-top: auto;">
                    <a href="<?= base_url('dakoii/logout') ?>" class="nav-link">
                        <span class="nav-icon">🚪</span>
                        <span class="nav-text">Logout</span>
                    </a>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="header">
                <div class="header-left">
                    <button class="sidebar-toggle" id="sidebarToggle">☰</button>
                    <h1 class="page-title"><?= isset($page_title) ? $page_title : 'Dashboard' ?></h1>
                </div>
                <div class="header-right">
                    <?= $this->renderSection('header_actions') ?>
                    <div class="user-menu">
                        <div class="user-avatar" title="<?= isset($user_name) ? $user_name : 'User' ?>">
                            <?= isset($user_name) ? strtoupper(substr($user_name, 0, 1)) : 'U' ?>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Content -->
            <div class="content">
                <!-- Flash Messages -->
                <?php if (session()->getFlashdata('success')): ?>
                    <div style="background: rgba(40, 167, 69, 0.1); border: 1px solid rgba(40, 167, 69, 0.3); color: #28a745; padding: var(--spacing-md); border-radius: var(--radius-md); margin-bottom: var(--spacing-lg);">
                        <?= session()->getFlashdata('success') ?>
                    </div>
                <?php endif; ?>

                <?php if (session()->getFlashdata('error')): ?>
                    <div style="background: rgba(220, 53, 69, 0.1); border: 1px solid rgba(220, 53, 69, 0.3); color: #dc3545; padding: var(--spacing-md); border-radius: var(--radius-md); margin-bottom: var(--spacing-lg);">
                        <?= session()->getFlashdata('error') ?>
                    </div>
                <?php endif; ?>

                <?php if (session()->getFlashdata('errors')): ?>
                    <div style="background: rgba(220, 53, 69, 0.1); border: 1px solid rgba(220, 53, 69, 0.3); color: #dc3545; padding: var(--spacing-md); border-radius: var(--radius-md); margin-bottom: var(--spacing-lg);">
                        <ul style="margin: 0; padding-left: var(--spacing-md);">
                            <?php foreach (session()->getFlashdata('errors') as $error): ?>
                                <li><?= esc($error) ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <?= $this->renderSection('content') ?>
            </div>
        </main>
    </div>

    <script>
        // Sidebar Toggle
        document.getElementById('sidebarToggle').addEventListener('click', function() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('collapsed');
            
            // Store preference
            localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
        });

        // Restore sidebar state
        document.addEventListener('DOMContentLoaded', function() {
            const isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
            if (isCollapsed) {
                document.getElementById('sidebar').classList.add('collapsed');
            }
        });

        // Mobile sidebar toggle
        function toggleMobileSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('mobile-open');
        }

        // Close mobile sidebar when clicking outside
        document.addEventListener('click', function(e) {
            const sidebar = document.getElementById('sidebar');
            const toggle = document.getElementById('sidebarToggle');
            
            if (window.innerWidth <= 768 && 
                !sidebar.contains(e.target) && 
                !toggle.contains(e.target) && 
                sidebar.classList.contains('mobile-open')) {
                sidebar.classList.remove('mobile-open');
            }
        });
    </script>
    <?= isset($additional_js) ? $additional_js : '' ?>
</body>
<hr style="border: none; border-top: 1px solid var(--glass-border); margin: 0;">
<footer style="width:100%;text-align:center;padding:1.25rem 0;color:var(--text-secondary);background:rgba(10,14,39,0.97);font-size:0.93rem;letter-spacing:0.01em;">
    Developed and maintained by <strong>Dakoii Systems</strong>.<br>
    &copy; <?= date('Y') ?> All rights reserved. Visit us at
    <a href="https://www.dakoiims.com" target="_blank" style="color:#06FFA5;text-decoration:none;font-weight:500;">www.dakoiims.com</a>
</footer>
</html>
