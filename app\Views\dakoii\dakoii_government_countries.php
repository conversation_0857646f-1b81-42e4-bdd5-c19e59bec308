<?= $this->extend('templates/dakoii_portal_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('dakoii/government') ?>" class="btn btn-secondary">
    <i class="icon">←</i> Back to Overview
</a>
<a href="<?= base_url('dakoii/government/countries/create') ?>" class="btn btn-primary">
    <i class="icon">➕</i> Add Country
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Page Header -->
    <div style="margin-bottom: var(--spacing-xl);">
        <h1 style="color: var(--text-primary); margin-bottom: var(--spacing-sm); font-size: 2rem; display: flex; align-items: center; gap: var(--spacing-md);">
            <span style="font-size: 2.5rem;">🌍</span>
            Countries Management
        </h1>
        <p style="color: var(--text-secondary); font-size: 1rem;">
            Manage country-level administrative divisions and their geographic information.
        </p>
    </div>

    <!-- Statistics Card -->
    <div class="card" style="margin-bottom: var(--spacing-xl);">
        <div style="display: flex; align-items: center; gap: var(--spacing-xl);">
            <div>
                <div style="font-size: 2.5rem; font-weight: 700; background: var(--gradient-primary); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
                    <?= isset($countries) ? count($countries) : 0 ?>
                </div>
                <div style="color: var(--text-secondary); font-size: 0.875rem;">Total Countries</div>
            </div>
            <div style="flex: 1; height: 1px; background: var(--glass-border);"></div>
            <div style="text-align: right;">
                <div style="color: var(--text-secondary); font-size: 0.875rem;">Last Updated</div>
                <div style="color: var(--text-primary); font-weight: 600;"><?= date('M j, Y') ?></div>
            </div>
        </div>
    </div>

    <!-- Countries List -->
    <?php if (!empty($countries)): ?>
        <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(350px, 1fr)); gap: var(--spacing-lg);">
            <?php foreach ($countries as $country): ?>
                <div class="card">
                    <div style="display: flex; align-items: center; gap: var(--spacing-md); margin-bottom: var(--spacing-md); padding-bottom: var(--spacing-md); border-bottom: 1px solid var(--glass-border);">
                        <div style="width: 60px; height: 40px; background: var(--gradient-primary); border-radius: var(--radius-md); display: flex; align-items: center; justify-content: center; font-weight: bold; color: white; font-size: 0.875rem;">
                            <?= strtoupper(esc($country['iso2'])) ?>
                        </div>
                        <div style="flex: 1;">
                            <h3 style="margin: 0 0 var(--spacing-xs) 0; color: var(--text-primary); font-size: 1.25rem;"><?= esc($country['name']) ?></h3>
                            <p style="margin: 0; color: var(--text-secondary); font-size: 0.875rem;">ISO3: <?= strtoupper(esc($country['iso3'])) ?></p>
                        </div>
                    </div>

                    <div style="margin-bottom: var(--spacing-md);">
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: var(--spacing-xs) 0; border-bottom: 1px solid rgba(255, 255, 255, 0.1);">
                            <span style="font-weight: 500; color: var(--text-secondary);">ISO2 Code:</span>
                            <span style="color: var(--text-primary); font-family: var(--font-mono);"><?= strtoupper(esc($country['iso2'])) ?></span>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: var(--spacing-xs) 0; border-bottom: 1px solid rgba(255, 255, 255, 0.1);">
                            <span style="font-weight: 500; color: var(--text-secondary);">ISO3 Code:</span>
                            <span style="color: var(--text-primary); font-family: var(--font-mono);"><?= strtoupper(esc($country['iso3'])) ?></span>
                        </div>
                        <?php if (isset($country['provinces_count'])): ?>
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: var(--spacing-xs) 0;">
                            <span style="font-weight: 500; color: var(--text-secondary);">Provinces:</span>
                            <span style="color: var(--text-primary);"><?= number_format($country['provinces_count']) ?></span>
                        </div>
                        <?php endif; ?>
                    </div>

                    <div style="display: flex; gap: var(--spacing-xs); flex-wrap: wrap;">
                        <a href="<?= base_url('dakoii/government/countries/' . $country['id']) ?>" class="btn btn-primary" style="font-size: 0.8rem; padding: var(--spacing-xs) var(--spacing-sm);">
                            👁️ View
                        </a>
                        <a href="<?= base_url('dakoii/government/countries/' . $country['id'] . '/edit') ?>" class="btn btn-secondary" style="font-size: 0.8rem; padding: var(--spacing-xs) var(--spacing-sm);">
                            ✏️ Edit
                        </a>
                        <a href="<?= base_url('dakoii/government/provinces?country_id=' . $country['id']) ?>" class="btn btn-secondary" style="font-size: 0.8rem; padding: var(--spacing-xs) var(--spacing-sm);">
                            🏞️ Provinces
                        </a>
                        <form method="POST" action="<?= base_url('dakoii/government/countries/' . $country['id'] . '/delete') ?>" style="display: inline;">
                            <?= csrf_field() ?>
                            <button type="submit" class="btn" style="background: #dc3545; color: white; font-size: 0.8rem; padding: var(--spacing-xs) var(--spacing-sm);"
                                    onclick="return confirm('Are you sure you want to delete this country? This will also delete all associated provinces, districts, and LLGs.')"
                                    title="Delete Country">
                                🗑️ Delete
                            </button>
                        </form>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php else: ?>
        <div class="card" style="text-align: center; padding: var(--spacing-2xl);">
            <div style="font-size: 4rem; margin-bottom: var(--spacing-md); opacity: 0.5;">🌍</div>
            <h3 style="color: var(--text-primary); margin-bottom: var(--spacing-md);">No Countries Found</h3>
            <p style="color: var(--text-secondary); margin-bottom: var(--spacing-lg);">Start by adding your first country to the government structure.</p>
            <a href="<?= base_url('dakoii/government/countries/create') ?>" class="btn btn-primary">
                <i class="icon">➕</i> Add First Country
            </a>
        </div>
    <?php endif; ?>
</div>
<?= $this->endSection() ?>