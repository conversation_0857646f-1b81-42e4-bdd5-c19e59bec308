<?php

namespace App\Models;

/**
 * Project Evidence Model
 * 
 * Handles file uploads and evidence documentation for project milestones.
 * Supports various file types with metadata tracking.
 */
class ProjectEvidenceModel extends BaseModel
{
    protected $table      = 'project_evidence';
    protected $primaryKey = 'id';
    
    protected $allowedFields = [
        'project_id', 'milestone_id', 'file_name', 'file_path', 'file_type',
        'file_size', 'description', 'uploaded_by', 'deleted_by'
    ];
    
    protected $useTimestamps = true;
    protected $useSoftDeletes = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';
    
    protected $validationRules = [
        'project_id'   => 'required|integer',
        'milestone_id' => 'required|integer',
        'file_name'    => 'required|max_length[255]',
        'file_path'    => 'required|max_length[500]',
        'file_type'    => 'required|max_length[50]',
        'file_size'    => 'required|integer'
    ];
    
    protected $validationMessages = [
        'project_id' => [
            'required' => 'Project ID is required'
        ],
        'milestone_id' => [
            'required' => 'Milestone ID is required'
        ],
        'file_name' => [
            'required' => 'File name is required'
        ],
        'file_path' => [
            'required' => 'File path is required'
        ]
    ];
    
    /**
     * Get evidence by project
     */
    public function getByProject(int $projectId): array
    {
        return $this->select('project_evidence.*, project_milestones.title as milestone_title, project_milestones.milestone_code')
                   ->join('project_milestones', 'project_milestones.id = project_evidence.milestone_id')
                   ->where('project_evidence.project_id', $projectId)
                   ->orderBy('project_evidence.created_at', 'DESC')
                   ->findAll();
    }
    
    /**
     * Get evidence by milestone
     */
    public function getByMilestone(int $milestoneId): array
    {
        return $this->where('milestone_id', $milestoneId)
                   ->orderBy('created_at', 'DESC')
                   ->findAll();
    }
    
    /**
     * Get evidence by file type
     */
    public function getByFileType(string $fileType): array
    {
        return $this->where('file_type', $fileType)
                   ->orderBy('created_at', 'DESC')
                   ->findAll();
    }
    
    /**
     * Upload evidence file
     */
    public function uploadEvidence(array $evidenceData): bool
    {
        $evidenceData['created_at'] = date('Y-m-d H:i:s');
        $evidenceData['uploaded_by'] = session()->get('admin_user_id');
        
        $result = $this->insert($evidenceData);
        
        if ($result) {
            // Update milestone evidence count
            $milestoneModel = new ProjectMilestoneModel();
            $count = $this->where('milestone_id', $evidenceData['milestone_id'])->countAllResults();
            $milestoneModel->updateEvidenceCount($evidenceData['milestone_id'], $count);
        }
        
        return $result !== false;
    }
    
    /**
     * Delete evidence file
     */
    public function deleteEvidence(int $evidenceId, ?int $deletedBy = null): bool
    {
        $evidence = $this->find($evidenceId);
        
        if (!$evidence) {
            return false;
        }
        
        // Soft delete the record
        $result = $this->update($evidenceId, [
            'deleted_by' => $deletedBy,
            'deleted_at' => date('Y-m-d H:i:s')
        ]);
        
        if ($result) {
            // Update milestone evidence count
            $milestoneModel = new ProjectMilestoneModel();
            $count = $this->where('milestone_id', $evidence['milestone_id'])->countAllResults();
            $milestoneModel->updateEvidenceCount($evidence['milestone_id'], $count);
            
            // Delete physical file
            if (file_exists($evidence['file_path'])) {
                unlink($evidence['file_path']);
            }
        }
        
        return $result;
    }
    
    /**
     * Get evidence statistics
     */
    public function getEvidenceStatistics(?int $projectId = null): array
    {
        $stats = [];
        
        $query = $this;
        if ($projectId) {
            $query = $query->where('project_id', $projectId);
        }
        
        // Total files
        $stats['total_files'] = $query->countAllResults();
        
        // Total file size
        $sizeResult = $query->selectSum('file_size')->first();
        $stats['total_size'] = $sizeResult['file_size'] ?? 0;
        
        // File type distribution
        $typeCounts = $this->select('file_type, COUNT(*) as count')
                          ->groupBy('file_type')
                          ->findAll();
        
        $stats['by_file_type'] = array_column($typeCounts, 'count', 'file_type');
        
        // Recent uploads (last 30 days)
        $recentDate = date('Y-m-d', strtotime('-30 days'));
        $recentQuery = $this->where('created_at >=', $recentDate);
        if ($projectId) {
            $recentQuery = $recentQuery->where('project_id', $projectId);
        }
        $stats['recent_uploads'] = $recentQuery->countAllResults();
        
        return $stats;
    }
    
    /**
     * Get large files (over specified size)
     */
    public function getLargeFiles(int $sizeThreshold = 10485760): array // 10MB default
    {
        return $this->where('file_size >', $sizeThreshold)
                   ->orderBy('file_size', 'DESC')
                   ->findAll();
    }
    
    /**
     * Get recent uploads
     */
    public function getRecentUploads(int $days = 7, ?int $projectId = null): array
    {
        $dateThreshold = date('Y-m-d', strtotime("-{$days} days"));
        
        $query = $this->select('project_evidence.*, project_milestones.title as milestone_title, projects.title as project_title')
                     ->join('project_milestones', 'project_milestones.id = project_evidence.milestone_id')
                     ->join('projects', 'projects.id = project_evidence.project_id')
                     ->where('project_evidence.created_at >=', $dateThreshold);
        
        if ($projectId) {
            $query = $query->where('project_evidence.project_id', $projectId);
        }
        
        return $query->orderBy('project_evidence.created_at', 'DESC')->findAll();
    }
    
    /**
     * Check file exists
     */
    public function fileExists(string $filePath): bool
    {
        return $this->where('file_path', $filePath)->countAllResults() > 0;
    }
    
    /**
     * Get evidence with milestone and project info
     */
    public function getEvidenceWithDetails(int $evidenceId): ?array
    {
        return $this->select('project_evidence.*, project_milestones.title as milestone_title, project_milestones.milestone_code, projects.title as project_title, projects.pro_code')
                   ->join('project_milestones', 'project_milestones.id = project_evidence.milestone_id')
                   ->join('projects', 'projects.id = project_evidence.project_id')
                   ->where('project_evidence.id', $evidenceId)
                   ->first();
    }
    
    /**
     * Update evidence description
     */
    public function updateDescription(int $evidenceId, string $description): bool
    {
        return $this->update($evidenceId, [
            'description' => $description,
            'updated_at' => date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * Get evidence count by milestone
     */
    public function getEvidenceCountByMilestone(int $milestoneId): int
    {
        return $this->where('milestone_id', $milestoneId)->countAllResults();
    }
    
    /**
     * Cleanup orphaned files
     */
    public function cleanupOrphanedFiles(): int
    {
        $orphanedFiles = $this->onlyDeleted()->findAll();
        $cleanedCount = 0;
        
        foreach ($orphanedFiles as $file) {
            if (file_exists($file['file_path'])) {
                if (unlink($file['file_path'])) {
                    $cleanedCount++;
                }
            }
        }
        
        return $cleanedCount;
    }
}
