<?php
// Email Template Preview
// This file allows you to preview the professional email templates

// Include CodeIgniter bootstrap
require_once __DIR__ . '/../app/Config/Paths.php';
$paths = new Config\Paths();
require_once $paths->systemDirectory . '/bootstrap.php';

// Initialize CodeIgniter
$app = Config\Services::codeigniter();
$app->initialize();

$template = $_GET['template'] ?? 'activation';

echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
.preview-header { background: #fff; padding: 20px; margin-bottom: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
.preview-nav { margin-bottom: 20px; }
.preview-nav a { 
    display: inline-block; 
    padding: 10px 20px; 
    margin-right: 10px; 
    background: #007bff; 
    color: white; 
    text-decoration: none; 
    border-radius: 4px; 
}
.preview-nav a.active { background: #0056b3; }
.email-preview { background: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
</style>";

echo "<div class='preview-header'>";
echo "<h1>Dakoii Portal - Email Template Preview</h1>";
echo "<p>Preview the professional email templates used by the Dakoii Portal system.</p>";
echo "</div>";

echo "<div class='preview-nav'>";
echo "<a href='?template=activation'" . ($template === 'activation' ? " class='active'" : "") . ">Account Activation</a>";
echo "<a href='?template=temp_password'" . ($template === 'temp_password' ? " class='active'" : "") . ">Temporary Password</a>";
echo "<a href='?template=password_reset'" . ($template === 'password_reset' ? " class='active'" : "") . ">Password Reset</a>";
echo "<a href='?template=organization_admin'" . ($template === 'organization_admin' ? " class='active'" : "") . ">Organization Admin</a>";
echo "</div>";

echo "<div class='email-preview'>";

// Sample data for each template
switch ($template) {
    case 'activation':
        $data = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'username' => 'johndoe',
            'role' => 'User',
            'activation_url' => 'http://localhost/promis_two/dakoii/users/activate/sample-token-123',
            'subject' => 'Activate Your Dakoii Portal Account'
        ];
        echo view('emails/activation_email', $data);
        break;
        
    case 'temp_password':
        $data = [
            'name' => 'Jane Smith',
            'email' => '<EMAIL>',
            'username' => 'janesmith',
            'temp_password' => '1234',
            'subject' => 'Dakoii Portal - Password Reset'
        ];
        echo view('emails/temp_password_email', $data);
        break;
        
    case 'password_reset':
        $data = [
            'name' => 'Bob Johnson',
            'username' => 'bobjohnson',
            'email' => '<EMAIL>',
            'reset_url' => 'http://localhost/promis_two/dakoii/password-reset/sample-reset-token-456',
            'subject' => 'Dakoii Portal - Password Reset Request'
        ];
        echo view('emails/password_reset_email', $data);
        break;
        
    case 'organization_admin':
        $data = [
            'name' => 'Alice Administrator',
            'organization_name' => 'Sample Organization Ltd.',
            'username' => 'aliceadmin',
            'email' => '<EMAIL>',
            'temp_password' => '5678',
            'subject' => 'Welcome to Dakoii Portal - Administrator Account Created'
        ];
        echo view('emails/organization_admin_email', $data);
        break;
        
    default:
        echo "<p>Invalid template selected.</p>";
}

echo "</div>";

echo "<div style='margin-top: 20px; padding: 15px; background: #e9ecef; border-radius: 4px;'>";
echo "<p><strong>Note:</strong> This is a preview of the email templates. The actual emails sent by the system will contain real data and working links.</p>";
echo "<p><strong>Features:</strong></p>";
echo "<ul>";
echo "<li>Professional design with Dakoii branding</li>";
echo "<li>Responsive layout that works on all devices</li>";
echo "<li>Clear call-to-action buttons</li>";
echo "<li>Security notices and instructions</li>";
echo "<li>4-digit temporary passwords for better usability</li>";
echo "</ul>";
echo "</div>";
?>
