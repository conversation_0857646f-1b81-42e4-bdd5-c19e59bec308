<?php

namespace App\Models;

/**
 * Project Budget Model
 * 
 * Handles project budget tracking with categories and expenditure monitoring.
 * Supports budget allocation, spending tracking, and variance analysis.
 */
class ProjectBudgetModel extends BaseModel
{
    protected $table      = 'project_budgets';
    protected $primaryKey = 'id';
    
    protected $allowedFields = [
        'project_id', 'category', 'allocated_amount', 'spent_amount',
        'remaining_amount', 'currency', 'notes', 'created_by', 'updated_by', 'deleted_by'
    ];
    
    protected $useTimestamps = true;
    protected $useSoftDeletes = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';
    
    protected $validationRules = [
        'project_id'       => 'required|integer',
        'category'         => 'required|max_length[100]',
        'allocated_amount' => 'required|decimal',
        'spent_amount'     => 'decimal',
        'currency'         => 'required|max_length[3]'
    ];
    
    protected $validationMessages = [
        'project_id' => [
            'required' => 'Project ID is required'
        ],
        'category' => [
            'required' => 'Budget category is required'
        ],
        'allocated_amount' => [
            'required' => 'Allocated amount is required'
        ]
    ];
    
    protected $beforeInsert = ['calculateRemaining'];
    protected $beforeUpdate = ['calculateRemaining'];
    
    /**
     * Calculate remaining amount before save
     */
    protected function calculateRemaining(array $data): array
    {
        if (isset($data['data']['allocated_amount']) && isset($data['data']['spent_amount'])) {
            $allocated = (float) $data['data']['allocated_amount'];
            $spent = (float) $data['data']['spent_amount'];
            $data['data']['remaining_amount'] = $allocated - $spent;
        }
        
        return $data;
    }
    
    /**
     * Get budget by project
     */
    public function getByProject(int $projectId): array
    {
        return $this->where('project_id', $projectId)
                   ->orderBy('category', 'ASC')
                   ->findAll();
    }
    
    /**
     * Get budget by category
     */
    public function getByCategory(string $category): array
    {
        return $this->where('category', $category)
                   ->orderBy('allocated_amount', 'DESC')
                   ->findAll();
    }
    
    /**
     * Get project total budget
     */
    public function getProjectTotalBudget(int $projectId): array
    {
        $result = $this->select('
                SUM(allocated_amount) as total_allocated,
                SUM(spent_amount) as total_spent,
                SUM(remaining_amount) as total_remaining,
                currency
            ')
            ->where('project_id', $projectId)
            ->groupBy('currency')
            ->findAll();
        
        return $result;
    }
    
    /**
     * Update spent amount
     */
    public function updateSpentAmount(int $budgetId, float $spentAmount, ?int $updatedBy = null): bool
    {
        $budget = $this->find($budgetId);
        
        if (!$budget) {
            return false;
        }
        
        $remainingAmount = $budget['allocated_amount'] - $spentAmount;
        
        return $this->update($budgetId, [
            'spent_amount' => $spentAmount,
            'remaining_amount' => $remainingAmount,
            'updated_by' => $updatedBy
        ]);
    }
    
    /**
     * Add expenditure to budget
     */
    public function addExpenditure(int $budgetId, float $amount, string $description = '', ?int $updatedBy = null): bool
    {
        $budget = $this->find($budgetId);
        
        if (!$budget) {
            return false;
        }
        
        $newSpentAmount = $budget['spent_amount'] + $amount;
        $newRemainingAmount = $budget['allocated_amount'] - $newSpentAmount;
        
        $updateData = [
            'spent_amount' => $newSpentAmount,
            'remaining_amount' => $newRemainingAmount,
            'updated_by' => $updatedBy
        ];
        
        if ($description) {
            $updateData['notes'] = $budget['notes'] . "\n" . date('Y-m-d H:i:s') . ": {$description}";
        }
        
        return $this->update($budgetId, $updateData);
    }
    
    /**
     * Get budget variance analysis
     */
    public function getBudgetVariance(int $projectId): array
    {
        $budgets = $this->getByProject($projectId);
        $analysis = [];
        
        foreach ($budgets as $budget) {
            $variance = $budget['allocated_amount'] - $budget['spent_amount'];
            $variancePercentage = $budget['allocated_amount'] > 0 
                ? ($variance / $budget['allocated_amount']) * 100 
                : 0;
            
            $analysis[] = [
                'category' => $budget['category'],
                'allocated' => $budget['allocated_amount'],
                'spent' => $budget['spent_amount'],
                'variance' => $variance,
                'variance_percentage' => round($variancePercentage, 2),
                'status' => $this->getBudgetStatus($variancePercentage)
            ];
        }
        
        return $analysis;
    }
    
    /**
     * Get budget status based on variance
     */
    private function getBudgetStatus(float $variancePercentage): string
    {
        if ($variancePercentage < -10) {
            return 'over_budget';
        } elseif ($variancePercentage < 0) {
            return 'at_risk';
        } elseif ($variancePercentage > 20) {
            return 'under_utilized';
        } else {
            return 'on_track';
        }
    }
    
    /**
     * Get budget statistics
     */
    public function getBudgetStatistics(?int $projectId = null): array
    {
        $stats = [];
        
        $query = $this;
        if ($projectId) {
            $query = $query->where('project_id', $projectId);
        }
        
        // Total allocations and spending
        $totals = $query->select('
                SUM(allocated_amount) as total_allocated,
                SUM(spent_amount) as total_spent,
                SUM(remaining_amount) as total_remaining
            ')
            ->first();
        
        $stats['totals'] = $totals;
        
        // Budget by category
        $categoryStats = $this->select('category, SUM(allocated_amount) as allocated, SUM(spent_amount) as spent')
                             ->groupBy('category')
                             ->orderBy('allocated', 'DESC')
                             ->findAll();
        
        $stats['by_category'] = $categoryStats;
        
        // Over budget categories
        $stats['over_budget'] = $this->where('remaining_amount <', 0)->countAllResults();
        
        return $stats;
    }
    
    /**
     * Get budget utilization rate
     */
    public function getBudgetUtilization(int $projectId): float
    {
        $totals = $this->select('SUM(allocated_amount) as allocated, SUM(spent_amount) as spent')
                      ->where('project_id', $projectId)
                      ->first();
        
        if ($totals['allocated'] == 0) {
            return 0.0;
        }
        
        return round(($totals['spent'] / $totals['allocated']) * 100, 2);
    }
    
    /**
     * Get categories with budget issues
     */
    public function getBudgetIssues(int $projectId): array
    {
        return $this->where('project_id', $projectId)
                   ->where('remaining_amount <', 0)
                   ->orderBy('remaining_amount', 'ASC')
                   ->findAll();
    }
    
    /**
     * Transfer budget between categories
     */
    public function transferBudget(int $fromBudgetId, int $toBudgetId, float $amount, ?int $updatedBy = null): bool
    {
        $fromBudget = $this->find($fromBudgetId);
        $toBudget = $this->find($toBudgetId);
        
        if (!$fromBudget || !$toBudget || $fromBudget['remaining_amount'] < $amount) {
            return false;
        }
        
        $this->db->transStart();
        
        // Reduce from source budget
        $this->update($fromBudgetId, [
            'allocated_amount' => $fromBudget['allocated_amount'] - $amount,
            'remaining_amount' => $fromBudget['remaining_amount'] - $amount,
            'updated_by' => $updatedBy
        ]);
        
        // Add to destination budget
        $this->update($toBudgetId, [
            'allocated_amount' => $toBudget['allocated_amount'] + $amount,
            'remaining_amount' => $toBudget['remaining_amount'] + $amount,
            'updated_by' => $updatedBy
        ]);
        
        $this->db->transComplete();
        
        return $this->db->transStatus();
    }
}
