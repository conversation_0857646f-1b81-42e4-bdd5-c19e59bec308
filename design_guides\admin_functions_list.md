PROMIS Administration Portal Features
Core Project Management Functions

Manage Project Details

Purpose: Create and update project information including title, goal, location (address, GPS coordinates, KML), project IDs, and other preset data.
Navigation: Dashboard → Manage Projects → Add New Project or View Project Profile → Project Details
Behavior: Auto-generates project code (PROJECT_ID + CURRENT_YEAR). Redirects to View Project Profile after creation/update. Uses CodeIgniter 4 form submission, no AJAX.
Interface: Bootstrap 5 form with validation; mobile-responsive.


View Project List

Purpose: Display all projects in a searchable, filterable table with status badges.
Navigation: Dashboard → Manage Projects
Behavior: Includes quick action buttons for View Profile/Report. Uses Bootstrap 5 badges (e.g., green=active, red=completed).
Interface: Card-based table with filters; mobile-responsive.


Manage Project Contractors

Purpose: Assign/remove contractors to projects, track active/inactive status with client satisfaction flags.
Navigation: Dashboard → Manage Projects → View Project Profile → Project Contractors
Behavior: Removed contractors marked is_active=0 with mandatory removal reason and document upload. Client satisfaction flags (e.g., positive, neutral, negative) recorded. Read-only in Project Monitoring Portal.
Interface: List view with status badges and satisfaction flags; modal for removal reasons.


Manage Project Officers

Purpose: Assign/remove project officers with role designation (lead, certifier, support).
Navigation: Dashboard → Manage Projects → View Project Profile → Project Officers
Behavior: Only users with is_project_officer=1 assignable. First assigned defaults to "lead." Other roles: certifier, support. Removed officers marked with remarks. Read-only in Project Monitoring Portal.
Interface: List view with role indicators (e.g., color-coded pills for lead/certifier/support); modal for removal reasons.


Manage Project Finances

Purpose: CRUD budget items (preset data) and expense records with supporting documents.
Navigation: Dashboard → Manage Projects → View Project Profile → Project Finances
Behavior: Budget items are preset data, fully CRUD in Admin Portal without approval. Removed budget items marked status=removed and striked out (CSS text-decoration: line-through). Expense records include file uploads. Read-only in Project Monitoring Portal.
Interface: Tabbed interface for budget/expenses; file upload widget.


Manage Project Outcomes

Purpose: Define measurable deliverables (e.g., "1 x bridge").
Navigation: Dashboard → Manage Projects → View Project Profile → Project Outcomes
Behavior: CRUD in Admin Portal; read-only in Project Monitoring Portal.
Interface: List view with add/edit buttons; mobile-responsive.


Manage Issues Addressed

Purpose: Document direct/indirect issues the project addresses.
Navigation: Dashboard → Manage Projects → View Project Profile → Issues Addressed
Behavior: Categorized as direct/indirect. CRUD in Admin Portal; read-only in Project Monitoring Portal.
Interface: Categorized list view; modal for edits.


Manage Impact Indicators

Purpose: Define baseline, target values, and dates for M&E.
Navigation: Dashboard → Manage Projects → View Project Profile → Impact Indicators
Behavior: Admins CRUD baseline/target; M&E adds actual values/dates. Read-only in Project Monitoring Portal.
Interface: Table with baseline/target/actual columns; modal for updates.


Manage Project Phases

Purpose: Create/organize project phases as milestone containers.
Navigation: Dashboard → Manage Projects → View Project Profile → Project Phases & Milestones
Behavior: CRUD in Admin Portal; read-only in Project Monitoring Portal.
Interface: Hierarchical list view; modal for phase creation.


Manage Project Milestones

Purpose: CRUD milestones within phases with target dates.
Navigation: Dashboard → Manage Projects → View Project Profile → Project Phases & Milestones → [Select Phase]
Behavior: Admins CRUD milestones; project officers update evidence (e.g., images, completion status) in Project Monitoring Portal, requiring admin approval.
Interface: Nested list under phases; modal for milestone edits.


Manage Project Documents

Purpose: Store and manage project-related files (designs, reports).
Navigation: Dashboard → Manage Projects → View Project Profile → Project Documents/Files
Behavior: Supports version tracking; file type validation. Read-only in Project Monitoring Portal.
Interface: File explorer-style interface; upload widget.


Manage Project Risks

Purpose: Document proposed/foreseen/witnessed risks.
Navigation: Dashboard → Manage Projects → View Project Profile → Project Risks
Behavior: Admins and officers can add risks; only admins can delete. All risk posts from Project Monitoring Portal require admin approval. Read-only in Project Monitoring Portal for non-risk updates.
Interface: List view with risk level indicators; modal for edits.


Generate Project Certificates

Purpose: Generate completion certificates for the contracting organization and project officers upon project completion.
Navigation: Dashboard → Manage Projects → View Project Profile → Generate Certificates (appears after all milestones completed)
Behavior: Triggered after all milestones are marked completed, M&E approves, and rates. Generates one certificate per project for the contractor and one for each project officer (lead, certifier, support). Certificates generated only once. Logged in audit trail.
Interface: Button appears on project profile when eligible; modal confirms certificate generation.



Contractor Management Functions

Manage Contractor Profiles

Purpose: Create/update contractor data (name, registration, services, tax details, client satisfaction flags).
Navigation: Dashboard → Manage Contractors → Add Contractor or View Contractor Profile
Behavior: Lists all projects (active/removed) and client satisfaction flags (positive, neutral, negative). Redirects to View Contractor Profile after creation/update. Uses CodeIgniter 4 form submission, no AJAX.
Interface: Form with validation; modal for document uploads and satisfaction flags.


View Contractor List

Purpose: Display all contractors with filters and compliance widgets.
Navigation: Dashboard → Manage Contractors
Behavior: Includes status badges (Blue=Active, Red=Terminated, etc.) and widgets for expired documents/workload.
Interface: Card-based grid with right-hand details pane; mobile-responsive.


Manage Contractor Documents

Purpose: Upload/track/expire statutory documents (licenses, insurance).
Navigation: Dashboard → Manage Contractors → View Contractor Profile → Document Vault
Behavior: Files stored once, referenced across projects. Auto-flags expired documents.
Interface: Document list with expiration status; upload modal.


Assign Contractor to Projects

Purpose: Link contractors to projects with role/service specification.
Navigation: Dashboard → Manage Contractors → View Contractor Profile → Project Assignment
Behavior: Tracks current/historical assignments. Removal sets is_active=0 with reason.
Interface: Project assignment list; modal for adding/removing.



User Management Functions

Manage User Accounts

Purpose: Create/update user accounts, roles, and permissions.
Navigation: Dashboard → Manage Users → Add User or Edit User
Behavior: Two-step wizard (Account Details, Roles & Permissions). Roles: Admin, Moderator, Editor, Viewer. Flags: is_supervisor, is_project_officer, is_mon_eval. Uses CodeIgniter 4 form submission.
Interface: Wizard-based form; role selection dropdown.


View User List

Purpose: Display all users with role/status filters and session details.
Navigation: Dashboard → Manage Users
Behavior: Shows active sessions, last login, assigned projects. Uses role-based color pills.
Interface: Tabular list with filters; mobile-responsive.


Manage User Sessions

Purpose: View/terminate active user sessions.
Navigation: Dashboard → Manage Users → Active Sessions
Behavior: Shows IP, login time, duration. Allows forced logout.
Interface: Session list with terminate button.


User Audit Trail

Purpose: Track all user actions (CRUD, logins, exports, permission changes, certificate generations) in user_audit_log table for security/compliance.
Navigation: Dashboard → Manage Users → Audit Log
Behavior: Logs user_id, action, table, record_id, timestamp. Filterable by action, user, date.
Interface: Filterable log table; exportable to CSV.


Password Reset Management

Purpose: Admin-initiated password resets with time-bound links.
Navigation: Dashboard → Manage Users → [Select User] → Reset Password
Behavior: Sends email with temporary link; uses Argon2 hashing. Logged in audit trail.
Interface: Button-triggered modal for reset confirmation.



Reporting Functions

Manage Reports
Purpose: Generate and view organization/project reports with export options.
Navigation: Dashboard → Manage Reports
Behavior: Tabs for Organization Overview, Project Reports, Financial Reports, Contractor Reports, M&E Reports, Custom Reports. Supports PDF/CSV/Excel exports.
Interface: Bootstrap 5 dashboard with interactive charts; sidebar filters.



Approval Workflow Functions

Monitoring Post Approval Queue

Purpose: Review/approve all monitoring posts (milestone updates, risks, payments, files, images, events, feedback) submitted via Project Monitoring Portal.
Navigation: Dashboard → Pending Approvals → Monitoring Posts
Behavior: Admins approve/reject posts. Milestone completions trigger M&E rating and certificate generation eligibility. Sends email/in-app notifications. Logged in audit trail.
Interface: Approval queue with approve/reject buttons; modal for remarks.


Document Approval Queue

Purpose: Review/approve uploaded project documents.
Navigation: Dashboard → Pending Approvals → Documents
Behavior: Allows revision requests. Logs approvals in audit trail.
Interface: Document list with approval status; modal for actions.



Authentication & Access Control Functions

User Login

Purpose: Authenticate users and redirect to Admin Portal if not project officers (is_project_officer=0) or Project Monitoring Portal if project officers.
Navigation: Main Login Page → Role-based redirect
Behavior: Uses Argon2 hashing; tracks failed attempts; 30-minute timeout. Non-project officers land on Admin Portal dashboard. Logged in audit trail.
Interface: Bootstrap 5 login form; mobile-responsive.


Organization Login Route

Purpose: Dedicated login endpoint for organization administrators.
Navigation: /organization/login → Admin Portal
Behavior: Supports activation/password reset emails. Logged in audit trail.
Interface: Same as main login but with organization branding.


Session Management

Purpose: Handle session timeouts and concurrent login restrictions.
Navigation: Background process
Behavior: 30-minute idle timeout; prevents concurrent logins.
Interface: None (background process).


Logout Function

Purpose: Terminate user session securely.
Navigation: User Menu → Logout
Behavior: Clears session data; redirects to login page. Logged in audit trail.
Interface: Button in user menu.


Remember Me

Purpose: Extend session persistence for trusted devices.
Navigation: Login Page → Remember Me Checkbox
Behavior: Creates 30-day secure cookie; revocable remotely. Logged in audit trail.
Interface: Checkbox on login form.



Audit & Logging Functions

System Audit Trail
Purpose: Log all user actions (CRUD, logins, exports, permission changes, certificate generations) in user_audit_log table for security/compliance.
Navigation: Dashboard → System Settings → Security Logs
Behavior: Captures user_id, action, table, record_id, timestamp. Filterable by action, user, date. Exportable to CSV.
Interface: Filterable log table; mobile-responsive.



Notification & Communication Functions

Notification System
Purpose: Send email and in-app notifications for system events (approvals, assignments, certificate generation).
Navigation: Notification bell (in-app); Dashboard → Communications → Broadcast
Behavior: Uses queue for email delivery; supports role/project targeting. Broadcast messages for organization-wide announcements.
Interface: Dropdown for in-app notifications; broadcast message form.



Data Management Functions

Data Import Wizard

Purpose: Bulk import projects, contractors, users from Excel/CSV.
Navigation: Each module → Import Data
Behavior: Validates formats; provides error reports; supports rollback. Logged in audit trail.
Interface: File upload with validation feedback.


Data Export Scheduler

Purpose: Schedule automated report/data exports.
Navigation: Dashboard → System Settings → Scheduled Tasks
Behavior: Supports PDF/CSV/Excel; can email results. Logged in audit trail.
Interface: Scheduler panel with format options.


Recycle Bin

Purpose: Recover soft-deleted items within 30 days.
Navigation: Dashboard → Recycle Bin
Behavior: Auto-deletes after retention period. Logged in audit trail.
Interface: List view with restore/delete options.



Search & Filter Functions

Global Search

Purpose: Search across all modules (projects, contractors, users, documents).
Navigation: Header search bar
Behavior: Indexes text fields; supports module filters.
Interface: Search bar with dropdown results.


Advanced Filter Builder

Purpose: Create complex filter combinations for data tables.
Navigation: Any data table → Filter icon → Advanced Filters
Behavior: Saves filter presets; supports AND/OR logic.
Interface: Drag-and-drop filter builder.


Saved Searches

Purpose: Save frequently used search criteria.
Navigation: Search results → Save Search
Behavior: Supports personal/shared searches; can set as default.
Interface: Saved search list with edit/delete options.



User Interface Functions

Dashboard Customization

Purpose: Allow users to customize dashboard widgets/layout.
Navigation: Dashboard → Customize Dashboard
Behavior: Drag-and-drop widgets; saves per user.
Interface: Customization panel with preview.


Theme Switcher

Purpose: Toggle between light/dark themes or custom themes.
Navigation: User Menu → Preferences → Theme
Behavior: Persists across sessions; uses Bootstrap 5 system theme color.
Interface: Theme selection dropdown.


Keyboard Shortcuts

Purpose: Quick navigation/actions for power users.
Navigation: Help Menu → Keyboard Shortcuts
Behavior: Customizable; modal shows available shortcuts.
Interface: Shortcut reference modal.



Help & Support Functions

Context-Sensitive Help

Purpose: Provide relevant help based on current page/action.
Navigation: Help icon → Help section
Behavior: Links to user manual; supports video tutorials.
Interface: Popover or modal with help content.


User Feedback System

Purpose: Collect user feedback/bug reports.
Navigation: Footer → Send Feedback
Behavior: Supports screenshot uploads; tracks feedback status.
Interface: Feedback form with file upload.



