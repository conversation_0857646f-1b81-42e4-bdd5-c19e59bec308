<?= $this->extend('templates/dakoii_portal_template') ?>



<?= $this->section('header_actions') ?>
<a href="<?= base_url('dakoii/users/' . $user['id']) ?>" class="btn btn-secondary">
    <i class="icon">←</i> Back to Profile
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<div class="fade-in">


    <!-- Edit User Form -->
    <div style="display: grid; grid-template-columns: 2fr 1fr; gap: var(--spacing-xl); max-width: 1200px; margin: 0 auto;">
        <div class="card">
            <div class="card-header">Edit User Profile</div>
            <p style="color: var(--text-secondary); margin-bottom: var(--spacing-lg);">Update user information and settings for <?= esc($user['name']) ?></p>

            <form method="POST" action="<?= base_url('dakoii/users/' . $user['id'] . '/update') ?>" 
                  class="user-form" enctype="multipart/form-data">
                <?= csrf_field() ?>

                <!-- Current User Info -->
                <div class="current-info">
                    <div class="current-avatar">
                        <?php if (!empty($user['id_photo_path']) && file_exists(ROOTPATH . $user['id_photo_path'])): ?>
                            <img src="<?= base_url($user['id_photo_path']) ?>" alt="<?= esc($user['name']) ?>">
                        <?php else: ?>
                            <div class="avatar-placeholder">
                                <?= strtoupper(substr($user['name'], 0, 1)) ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="current-details">
                        <h3><?= esc($user['name']) ?></h3>
                        <p>User Code: <?= esc($user['user_code']) ?></p>
                        <p>Current Role: <span class="role-badge role-<?= $user['role'] ?>"><?= ucfirst($user['role']) ?></span></p>
                    </div>
                </div>

                <!-- Basic Information -->
                <div class="form-section">
                    <h3 style="margin: 0 0 var(--spacing-md) 0; color: var(--text-primary); font-size: 1.2rem;">Basic Information</h3>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-md); margin-bottom: var(--spacing-md);">
                        <div class="form-group">
                            <label for="name" class="form-label">Full Name *</label>
                            <input type="text" id="name" name="name" class="form-input"
                                   value="<?= old('name', $user['name']) ?>" required>
                        </div>

                        <div class="form-group">
                            <label for="email" class="form-label">Email Address *</label>
                            <input type="email" id="email" name="email" class="form-input"
                                   value="<?= old('email', $user['email']) ?>" required>
                        </div>
                    </div>

                    <?php if ($current_user['role'] === 'admin'): ?>
                    <div style="display: grid; grid-template-columns: 1fr; gap: var(--spacing-md);">
                        <div class="form-group">
                            <label for="username" class="form-label">Username *</label>
                            <input type="text" id="username" name="username" class="form-input"
                                   value="<?= old('username', $user['username']) ?>" required>
                            <small style="margin-top: var(--spacing-xs); font-size: 0.85rem; color: var(--text-secondary);">Only administrators can change usernames</small>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- Role and Permissions -->
                <?php 
                $canEditRole = false;
                if ($current_user['role'] === 'admin') {
                    $canEditRole = true;
                } elseif ($current_user['role'] === 'moderator' && $user['role'] !== 'admin') {
                    $canEditRole = true;
                }
                ?>

                <?php if ($canEditRole): ?>
                <div class="form-section" style="margin-bottom: var(--spacing-xl); padding-bottom: var(--spacing-lg); border-bottom: 1px solid var(--glass-border);">
                    <h3 style="margin: 0 0 var(--spacing-md) 0; color: var(--text-primary); font-size: 1.2rem;">Role and Permissions</h3>
                    
                    <div class="form-group">
                        <label for="role" class="form-label">System Role *</label>
                        <select id="role" name="role" class="form-input" required>
                            <?php if ($current_user['role'] === 'admin'): ?>
                                <option value="admin" <?= $user['role'] === 'admin' ? 'selected' : '' ?>>Administrator</option>
                            <?php endif; ?>
                            <option value="moderator" <?= $user['role'] === 'moderator' ? 'selected' : '' ?>>Moderator</option>
                            <option value="user" <?= $user['role'] === 'user' ? 'selected' : '' ?>>User</option>
                        </select>
                        <?php if ($current_user['role'] === 'moderator'): ?>
                            <small style="margin-top: var(--spacing-xs); font-size: 0.85rem; color: var(--text-secondary);">Moderators cannot promote users to administrator role</small>
                        <?php endif; ?>
                    </div>

                    <div class="role-permissions">
                        <div class="permission-preview" id="rolePermissions">
                            <!-- Will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Profile Photo -->
                <div class="form-section" style="margin-bottom: var(--spacing-xl);">
                    <h3 style="margin: 0 0 var(--spacing-md) 0; color: var(--text-primary); font-size: 1.2rem;">Profile Photo</h3>
                    
                    <div class="photo-upload">
                        <div class="current-photo">
                            <?php if (!empty($user['id_photo_path']) && file_exists(ROOTPATH . $user['id_photo_path'])): ?>
                                <img src="<?= base_url($user['id_photo_path']) ?>" alt="Current photo" id="currentPhoto">
                            <?php else: ?>
                                <div class="no-photo" id="currentPhoto">
                                    <span>No photo uploaded</span>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="upload-controls">
                            <input type="file" id="id_photo" name="id_photo" accept="image/*" class="file-input">
                            <label for="id_photo" class="file-label">
                                <i class="icon">📷</i> Choose New Photo
                            </label>
                            <small style="margin-top: var(--spacing-xs); font-size: 0.85rem; color: var(--text-secondary);">
                                Supported formats: JPG, PNG, GIF, WebP. Maximum size: 15MB.
                            </small>
                        </div>
                    </div>
                </div>

                <!-- Security Notice -->
                <div class="security-notice">
                    <div class="notice-icon">🔒</div>
                    <div class="notice-content">
                        <h4>Security Information</h4>
                        <ul>
                            <li>Changes to user roles require administrator approval</li>
                            <li>Email changes may require re-verification</li>
                            <li>All profile changes are logged for security auditing</li>
                            <li>Users can only edit their own basic information</li>
                        </ul>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="icon">💾</i> Save Changes
                    </button>
                    <a href="<?= base_url('dakoii/users/' . $user['id']) ?>" class="btn btn-secondary">
                        <i class="icon">✖️</i> Cancel
                    </a>
                </div>
            </form>
        </div>

        <!-- Edit Permissions Info -->
        <div class="info-card glass-effect">
            <div class="info-header">
                <h3>Edit Permissions</h3>
            </div>
            <div class="info-content">
                <div class="permission-info">
                    <?php if ($current_user['role'] === 'admin'): ?>
                        <div class="permission-level admin">
                            <h4>Administrator Access</h4>
                            <p>You can edit all user information including roles and security settings.</p>
                        </div>
                    <?php elseif ($current_user['role'] === 'moderator'): ?>
                        <div class="permission-level moderator">
                            <h4>Moderator Access</h4>
                            <p>You can edit user information except for administrator accounts.</p>
                        </div>
                    <?php else: ?>
                        <div class="permission-level user">
                            <h4>Self-Edit Access</h4>
                            <p>You can only edit your own basic profile information.</p>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="edit-restrictions">
                    <h4>Restrictions</h4>
                    <ul>
                        <?php if ($current_user['id'] == $user['id']): ?>
                            <li>Cannot change your own role</li>
                            <li>Cannot deactivate your own account</li>
                        <?php endif; ?>
                        <?php if ($current_user['role'] === 'moderator'): ?>
                            <li>Cannot edit administrator accounts</li>
                            <li>Cannot promote users to administrator</li>
                        <?php endif; ?>
                        <li>Username changes require administrator approval</li>
                        <li>Role changes are logged and audited</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>









<script>
// Role permissions preview
const rolePermissions = {
    admin: [
        '✅ Full system access and configuration',
        '✅ Manage all users and roles',
        '✅ Create, edit, and delete organizations',
        '✅ Manage government structure data',
        '✅ Access system logs and audit trails',
        '✅ Perform bulk operations',
        '✅ Reset passwords and manage security'
    ],
    moderator: [
        '✅ Manage users (except administrators)',
        '✅ Create and edit organizations',
        '✅ View government structure data',
        '✅ Access basic system reports',
        '❌ Cannot manage administrator accounts',
        '❌ Cannot access system configuration',
        '❌ Cannot perform bulk delete operations'
    ],
    user: [
        '✅ View system dashboard',
        '✅ View organization information',
        '✅ View government structure data',
        '✅ Edit own profile information',
        '❌ Cannot manage other users',
        '❌ Cannot create or edit organizations',
        '❌ Cannot access system administration'
    ]
};

function updateRolePermissions() {
    const roleSelect = document.getElementById('role');
    const permissionsDiv = document.getElementById('rolePermissions');
    
    if (roleSelect && permissionsDiv) {
        const selectedRole = roleSelect.value;
        const permissions = rolePermissions[selectedRole] || [];
        
        permissionsDiv.innerHTML = `
            <h4>${selectedRole.charAt(0).toUpperCase() + selectedRole.slice(1)} Permissions</h4>
            <ul>
                ${permissions.map(permission => `<li>${permission}</li>`).join('')}
            </ul>
        `;
    }
}

// Initialize role permissions display
document.addEventListener('DOMContentLoaded', function() {
    updateRolePermissions();
    
    const roleSelect = document.getElementById('role');
    if (roleSelect) {
        roleSelect.addEventListener('change', updateRolePermissions);
    }
});

// Photo preview
document.getElementById('id_photo').addEventListener('change', function(e) {
    const file = e.target.files[0];
    const currentPhoto = document.getElementById('currentPhoto');
    
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            currentPhoto.innerHTML = `<img src="${e.target.result}" alt="New photo preview">`;
        };
        reader.readAsDataURL(file);
    }
});

// Form validation
document.querySelector('.user-form').addEventListener('submit', function(e) {
    const name = document.getElementById('name').value.trim();
    const email = document.getElementById('email').value.trim();
    
    if (name.length < 2) {
        e.preventDefault();
        alert('Name must be at least 2 characters long.');
        return false;
    }
    
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
        e.preventDefault();
        alert('Please enter a valid email address.');
        return false;
    }
});
</script>
<?= $this->endSection() ?>
