<?= $this->extend('templates/dakoii_portal_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('dakoii/government') ?>" class="btn btn-secondary">
    <i class="icon">←</i> Back to Overview
</a>
<button class="btn btn-primary" onclick="exportChart()">
    <i class="icon">📤</i> Export Chart
</button>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Page Header -->
    <div style="margin-bottom: var(--spacing-xl);">
        <h1 style="color: var(--text-primary); margin-bottom: var(--spacing-sm); font-size: 2rem; display: flex; align-items: center; gap: var(--spacing-md);">
            <span style="font-size: 2.5rem;">📊</span>
            Government Hierarchy Chart
        </h1>
        <p style="color: var(--text-secondary); font-size: 1rem;">
            Interactive visualization of the complete government structure hierarchy.
        </p>
    </div>

    <!-- Chart Controls -->
    <div class="card" style="margin-bottom: var(--spacing-xl);">
        <div class="card-header">Chart Controls</div>
        <div style="display: flex; gap: var(--spacing-md); align-items: center; flex-wrap: wrap;">
            <div style="display: flex; align-items: center; gap: var(--spacing-sm);">
                <label class="form-label" style="margin: 0;">View Level:</label>
                <select id="chartLevel" class="form-input" style="width: auto;" onchange="updateChart()">
                    <option value="all">All Levels</option>
                    <option value="countries">Countries Only</option>
                    <option value="provinces">Countries → Provinces</option>
                    <option value="districts">Countries → Provinces → Districts</option>
                    <option value="llgs">Complete Hierarchy</option>
                </select>
            </div>
            <div style="display: flex; align-items: center; gap: var(--spacing-sm);">
                <label class="form-label" style="margin: 0;">Layout:</label>
                <select id="chartLayout" class="form-input" style="width: auto;" onchange="updateChart()">
                    <option value="tree">Tree View</option>
                    <option value="radial">Radial View</option>
                    <option value="horizontal">Horizontal Tree</option>
                </select>
            </div>
            <button class="btn btn-secondary" onclick="resetZoom()">
                <i class="icon">🔍</i> Reset Zoom
            </button>
            <button class="btn btn-secondary" onclick="expandAll()">
                <i class="icon">📂</i> Expand All
            </button>
            <button class="btn btn-secondary" onclick="collapseAll()">
                <i class="icon">📁</i> Collapse All
            </button>
        </div>
    </div>

    <!-- Hierarchy Chart -->
    <div class="card">
        <div class="card-header">Interactive Hierarchy Chart</div>
        <div id="hierarchy-chart" style="height: 600px; position: relative; overflow: hidden; border-radius: var(--radius-md);">
            <!-- Chart will be rendered here -->
            <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: var(--text-tertiary);">
                <div style="text-align: center;">
                    <div style="font-size: 4rem; margin-bottom: var(--spacing-md);">📊</div>
                    <h3 style="color: var(--text-primary); margin-bottom: var(--spacing-sm);">Loading Hierarchy Chart...</h3>
                    <p>Interactive government structure visualization will appear here.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Chart Legend -->
    <div class="card">
        <div class="card-header">Chart Legend</div>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: var(--spacing-md);">
            <div style="display: flex; align-items: center; gap: var(--spacing-sm);">
                <div style="width: 20px; height: 20px; background: var(--gradient-primary); border-radius: 50%;"></div>
                <span style="color: var(--text-secondary);">Countries</span>
            </div>
            <div style="display: flex; align-items: center; gap: var(--spacing-sm);">
                <div style="width: 20px; height: 20px; background: var(--gradient-secondary); border-radius: 50%;"></div>
                <span style="color: var(--text-secondary);">Provinces</span>
            </div>
            <div style="display: flex; align-items: center; gap: var(--spacing-sm);">
                <div style="width: 20px; height: 20px; background: var(--gradient-accent); border-radius: 50%;"></div>
                <span style="color: var(--text-secondary);">Districts</span>
            </div>
            <div style="display: flex; align-items: center; gap: var(--spacing-sm);">
                <div style="width: 20px; height: 20px; background: #06FFA5; border-radius: 50%;"></div>
                <span style="color: var(--text-secondary);">Local Level Governments</span>
            </div>
        </div>
    </div>
</div>

<script src="https://d3js.org/d3.v7.min.js"></script>
<script>
// Government Hierarchy Chart Implementation
let chartData = null;
let svg = null;
let currentLevel = 'all';
let currentLayout = 'tree';

document.addEventListener('DOMContentLoaded', function() {
    initializeChart();
    loadChartData();
});

function initializeChart() {
    const container = document.getElementById('hierarchy-chart');
    const width = container.clientWidth;
    const height = 600;

    svg = d3.select('#hierarchy-chart')
        .append('svg')
        .attr('width', width)
        .attr('height', height)
        .style('background', 'rgba(0, 0, 0, 0.1)')
        .style('border-radius', 'var(--radius-md)');

    // Add zoom behavior
    const zoom = d3.zoom()
        .scaleExtent([0.1, 3])
        .on('zoom', function(event) {
            svg.select('g').attr('transform', event.transform);
        });

    svg.call(zoom);

    // Add main group
    svg.append('g').attr('class', 'chart-group');
}

function loadChartData() {
    // Simulate loading chart data
    // In a real implementation, this would fetch from the server
    setTimeout(() => {
        chartData = {
            name: "Government Structure",
            children: [
                {
                    name: "Papua New Guinea",
                    type: "country",
                    children: [
                        {
                            name: "Western Province",
                            type: "province",
                            children: [
                                {
                                    name: "South Fly District",
                                    type: "district",
                                    children: [
                                        { name: "Daru Urban LLG", type: "llg" },
                                        { name: "Kiwai Rural LLG", type: "llg" }
                                    ]
                                }
                            ]
                        },
                        {
                            name: "National Capital District",
                            type: "province",
                            children: [
                                {
                                    name: "Port Moresby District",
                                    type: "district",
                                    children: [
                                        { name: "Port Moresby North East LLG", type: "llg" },
                                        { name: "Port Moresby South LLG", type: "llg" }
                                    ]
                                }
                            ]
                        }
                    ]
                }
            ]
        };

        updateChart();
    }, 1000);
}

function updateChart() {
    if (!chartData) return;

    const level = document.getElementById('chartLevel').value;
    const layout = document.getElementById('chartLayout').value;

    currentLevel = level;
    currentLayout = layout;

    // Clear existing chart
    svg.select('.chart-group').selectAll('*').remove();

    // Filter data based on selected level
    const filteredData = filterDataByLevel(chartData, level);

    // Render chart based on layout
    if (layout === 'tree') {
        renderTreeChart(filteredData);
    } else if (layout === 'radial') {
        renderRadialChart(filteredData);
    } else {
        renderHorizontalChart(filteredData);
    }
}

function filterDataByLevel(data, level) {
    // Implementation would filter the data based on the selected level
    return data; // Simplified for now
}

function renderTreeChart(data) {
    // D3.js tree chart implementation
    const width = svg.attr('width');
    const height = svg.attr('height');

    const treeLayout = d3.tree().size([width - 100, height - 100]);
    const root = d3.hierarchy(data);

    treeLayout(root);

    const g = svg.select('.chart-group')
        .attr('transform', 'translate(50, 50)');

    // Draw links
    g.selectAll('.link')
        .data(root.links())
        .enter()
        .append('path')
        .attr('class', 'link')
        .attr('d', d3.linkVertical()
            .x(d => d.x)
            .y(d => d.y))
        .style('fill', 'none')
        .style('stroke', 'rgba(255, 255, 255, 0.3)')
        .style('stroke-width', 2);

    // Draw nodes
    const nodes = g.selectAll('.node')
        .data(root.descendants())
        .enter()
        .append('g')
        .attr('class', 'node')
        .attr('transform', d => `translate(${d.x}, ${d.y})`);

    nodes.append('circle')
        .attr('r', 8)
        .style('fill', d => getNodeColor(d.data.type))
        .style('stroke', '#fff')
        .style('stroke-width', 2);

    nodes.append('text')
        .attr('dy', -15)
        .attr('text-anchor', 'middle')
        .style('fill', 'var(--text-primary)')
        .style('font-size', '12px')
        .text(d => d.data.name);
}

function renderRadialChart(data) {
    // Radial chart implementation
    renderTreeChart(data); // Simplified for now
}

function renderHorizontalChart(data) {
    // Horizontal chart implementation
    renderTreeChart(data); // Simplified for now
}

function getNodeColor(type) {
    switch(type) {
        case 'country': return '#FF006E';
        case 'province': return '#00D4FF';
        case 'district': return '#FFB700';
        case 'llg': return '#06FFA5';
        default: return '#B8BCC8';
    }
}

function resetZoom() {
    svg.transition().duration(750).call(
        d3.zoom().transform,
        d3.zoomIdentity
    );
}

function expandAll() {
    // Implementation to expand all nodes
    updateChart();
}

function collapseAll() {
    // Implementation to collapse all nodes
    updateChart();
}

function exportChart() {
    // Implementation to export chart as image
    alert('Chart export functionality will be implemented.');
}
</script>
<?= $this->endSection() ?>