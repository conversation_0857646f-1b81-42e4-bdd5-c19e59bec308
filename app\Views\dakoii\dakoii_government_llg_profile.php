<?= $this->extend('templates/dakoii_portal_template') ?>
<?= $this->section('content') ?>
<h1>LLG Profile</h1>
<?php if (isset($llg)): ?>
    <ul>
        <li><strong>Name:</strong> <?= esc($llg['name']) ?></li>
        <li><strong>LLG Code:</strong> <?= esc($llg['llg_code']) ?></li>
        <li><strong>District ID:</strong> <?= esc($llg['district_id']) ?></li>
    </ul>
    <a class="btn btn-secondary" href="<?= base_url('dakoii/government/llgs/'.$llg['id'].'/edit') ?>">Edit</a>
    <form action="<?= base_url('dakoii/government/llgs/'.$llg['id'].'/delete') ?>" method="post" style="display:inline;">
        <?= csrf_field() ?>
        <button type="submit" onclick="return confirm('Delete this LLG?')">Delete</button>
    </form>
<?php else: ?>
    <p>LLG not found.</p>
<?php endif; ?>
<?= $this->endSection() ?> 