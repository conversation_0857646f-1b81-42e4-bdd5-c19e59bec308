<?php
// PHP Upload Configuration Check
// This file helps diagnose file upload issues

echo "<h2>PHP Upload Configuration Check</h2>";

echo "<h3>Current PHP Settings:</h3>";
echo "<table border='1' cellpadding='5'>";
echo "<tr><th>Setting</th><th>Current Value</th><th>Recommended</th><th>Status</th></tr>";

$settings = [
    'file_uploads' => ['On', 'On'],
    'upload_max_filesize' => ['15M', '15M or higher'],
    'post_max_size' => ['20M', '20M or higher'],
    'max_execution_time' => ['60', '60 or higher'],
    'max_input_time' => ['60', '60 or higher'],
    'memory_limit' => ['128M', '128M or higher']
];

foreach ($settings as $setting => $recommended) {
    $current = ini_get($setting);
    $status = 'OK';
    
    // Check specific conditions
    if ($setting === 'file_uploads' && $current !== 'On') {
        $status = 'ERROR';
    } elseif ($setting === 'upload_max_filesize') {
        $currentBytes = return_bytes($current);
        $recommendedBytes = return_bytes('15M');
        if ($currentBytes < $recommendedBytes) {
            $status = 'WARNING';
        }
    } elseif ($setting === 'post_max_size') {
        $currentBytes = return_bytes($current);
        $recommendedBytes = return_bytes('20M');
        if ($currentBytes < $recommendedBytes) {
            $status = 'WARNING';
        }
    }
    
    $color = $status === 'OK' ? 'green' : ($status === 'WARNING' ? 'orange' : 'red');
    echo "<tr>";
    echo "<td>{$setting}</td>";
    echo "<td>{$current}</td>";
    echo "<td>{$recommended[1]}</td>";
    echo "<td style='color: {$color}'>{$status}</td>";
    echo "</tr>";
}

echo "</table>";

echo "<h3>Upload Directory Check:</h3>";
$uploadDirs = [
    'public/uploads/',
    'public/uploads/users/',
    'public/uploads/organizations/'
];

foreach ($uploadDirs as $dir) {
    $fullPath = __DIR__ . '/' . $dir;
    $exists = is_dir($fullPath);
    $writable = $exists ? is_writable($fullPath) : false;
    
    echo "<p><strong>{$dir}</strong>: ";
    if ($exists) {
        echo "Exists ✓ ";
        echo $writable ? "Writable ✓" : "Not Writable ✗";
    } else {
        echo "Does not exist ✗";
        // Try to create it
        if (mkdir($fullPath, 0755, true)) {
            echo " (Created successfully ✓)";
        } else {
            echo " (Failed to create ✗)";
        }
    }
    echo "</p>";
}

echo "<h3>Test File Upload:</h3>";
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['test_file'])) {
    $file = $_FILES['test_file'];
    echo "<p><strong>File Upload Test Results:</strong></p>";
    echo "<pre>";
    echo "File name: " . $file['name'] . "\n";
    echo "File size: " . $file['size'] . " bytes\n";
    echo "File type: " . $file['type'] . "\n";
    echo "Temp file: " . $file['tmp_name'] . "\n";
    echo "Error code: " . $file['error'] . "\n";
    
    if ($file['error'] === UPLOAD_ERR_OK) {
        echo "Upload successful! ✓\n";
    } else {
        echo "Upload error: " . getUploadErrorMessage($file['error']) . "\n";
    }
    echo "</pre>";
} else {
    echo '<form method="post" enctype="multipart/form-data">';
    echo '<input type="file" name="test_file" accept="image/*">';
    echo '<input type="submit" value="Test Upload">';
    echo '</form>';
}

function return_bytes($val) {
    $val = trim($val);
    $last = strtolower($val[strlen($val)-1]);
    $val = (int)$val;
    switch($last) {
        case 'g':
            $val *= 1024;
        case 'm':
            $val *= 1024;
        case 'k':
            $val *= 1024;
    }
    return $val;
}

function getUploadErrorMessage($error) {
    switch ($error) {
        case UPLOAD_ERR_OK:
            return 'No error';
        case UPLOAD_ERR_INI_SIZE:
            return 'File exceeds upload_max_filesize';
        case UPLOAD_ERR_FORM_SIZE:
            return 'File exceeds MAX_FILE_SIZE';
        case UPLOAD_ERR_PARTIAL:
            return 'File was only partially uploaded';
        case UPLOAD_ERR_NO_FILE:
            return 'No file was uploaded';
        case UPLOAD_ERR_NO_TMP_DIR:
            return 'Missing temporary folder';
        case UPLOAD_ERR_CANT_WRITE:
            return 'Failed to write file to disk';
        case UPLOAD_ERR_EXTENSION:
            return 'File upload stopped by extension';
        default:
            return 'Unknown upload error';
    }
}
?>
