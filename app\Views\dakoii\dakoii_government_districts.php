<?= $this->extend('templates/dakoii_portal_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('dakoii/government') ?>" class="btn btn-secondary">
    <i class="icon">←</i> Back to Overview
</a>
<a href="<?= base_url('dakoii/government/districts/create') ?>" class="btn btn-primary">
    <i class="icon">➕</i> Add Districts
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Page Header -->
    <div style="margin-bottom: var(--spacing-xl);">
        <h1 style="color: var(--text-primary); margin-bottom: var(--spacing-sm); font-size: 2rem; display: flex; align-items: center; gap: var(--spacing-md);">
            <span style="font-size: 2.5rem;">🏘️</span>
            Districts Management
        </h1>
        <p style="color: var(--text-secondary); font-size: 1rem;">
            Manage districts and their administrative information.
        </p>
    </div>

    <!-- Statistics Card -->
    <div class="card" style="margin-bottom: var(--spacing-xl);">
        <div style="display: flex; align-items: center; gap: var(--spacing-xl);">
            <div>
                <div style="font-size: 2.5rem; font-weight: 700; background: var(--gradient-primary); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
                    <?= isset($districts) ? count($districts) : 0 ?>
                </div>
                <div style="color: var(--text-secondary); font-size: 0.875rem;">Total Districts</div>
            </div>
            <div style="flex: 1; height: 1px; background: var(--glass-border);"></div>
            <div style="text-align: right;">
                <div style="color: var(--text-secondary); font-size: 0.875rem;">Last Updated</div>
                <div style="color: var(--text-primary); font-weight: 600;"><?= date('M j, Y') ?></div>
            </div>
        </div>
    </div>

    <!-- Districts List -->
    <?php if (!empty($districts)): ?>
        <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(350px, 1fr)); gap: var(--spacing-lg);">
            <?php foreach ($districts as $item): ?>
                <div class="card">
                    <div style="display: flex; align-items: center; gap: var(--spacing-md); margin-bottom: var(--spacing-md); padding-bottom: var(--spacing-md); border-bottom: 1px solid var(--glass-border);">
                        <div style="width: 60px; height: 40px; background: var(--gradient-primary); border-radius: var(--radius-md); display: flex; align-items: center; justify-content: center; font-weight: bold; color: white; font-size: 0.875rem;">
                            🏘️
                        </div>
                        <div style="flex: 1;">
                            <h3 style="margin: 0 0 var(--spacing-xs) 0; color: var(--text-primary); font-size: 1.25rem;"><?= esc($item['name']) ?></h3>
                            <?php if (isset($item['code'])): ?>
                            <p style="margin: 0; color: var(--text-secondary); font-size: 0.875rem;">Code: <?= esc($item['code']) ?></p>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div style="display: flex; gap: var(--spacing-xs); flex-wrap: wrap;">
                        <a href="<?= base_url('dakoii/government/districts/' . $item['id']) ?>" class="btn btn-primary" style="font-size: 0.8rem; padding: var(--spacing-xs) var(--spacing-sm);">
                            👁️ View
                        </a>
                        <a href="<?= base_url('dakoii/government/districts/' . $item['id'] . '/edit') ?>" class="btn btn-secondary" style="font-size: 0.8rem; padding: var(--spacing-xs) var(--spacing-sm);">
                            ✏️ Edit
                        </a>
                        <a href="<?= base_url('dakoii/government/district_id=' . $item['id']) ?>" class="btn btn-secondary" style="font-size: 0.8rem; padding: var(--spacing-xs) var(--spacing-sm);">
                            View Llgs
                        </a>
                        <form method="POST" action="<?= base_url('dakoii/government/districts/' . $item['id'] . '/delete') ?>" style="display: inline;">
                            <?= csrf_field() ?>
                            <button type="submit" class="btn" style="background: #dc3545; color: white; font-size: 0.8rem; padding: var(--spacing-xs) var(--spacing-sm);"
                                    onclick="return confirm('Are you sure you want to delete this district?')"
                                    title="Delete district">
                                🗑️ Delete
                            </button>
                        </form>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php else: ?>
        <div class="card" style="text-align: center; padding: var(--spacing-2xl);">
            <div style="font-size: 4rem; margin-bottom: var(--spacing-md); opacity: 0.5;">🏘️</div>
            <h3 style="color: var(--text-primary); margin-bottom: var(--spacing-md);">No Districts Found</h3>
            <p style="color: var(--text-secondary); margin-bottom: var(--spacing-lg);">Start by adding your first district to the government structure.</p>
            <a href="<?= base_url('dakoii/government/districts/create') ?>" class="btn btn-primary">
                <i class="icon">➕</i> Add First Districts
            </a>
        </div>
    <?php endif; ?>
</div>
<?= $this->endSection() ?>