<?php

namespace App\Models;

/**
 * Project Contractor Model
 * 
 * Handles many-to-many relationship between projects and contractors
 * with historical tracking and client feedback flags.
 */
class ProjectContractorModel extends BaseModel
{
    protected $table      = 'project_contractors';
    protected $primaryKey = 'id';
    
    protected $allowedFields = [
        'project_id', 'contractor_id', 'joined_at', 'is_active',
        'removal_reason', 'client_flag', 'created_by', 'deleted_by'
    ];
    
    protected $useTimestamps = false; // Custom timestamp handling
    protected $useSoftDeletes = true;
    protected $createdField  = 'created_at';
    protected $deletedField  = 'deleted_at';
    
    protected $validationRules = [
        'project_id'    => 'required|integer',
        'contractor_id' => 'required|integer',
        'joined_at'     => 'required|valid_date',
        'is_active'     => 'in_list[0,1]',
        'client_flag'   => 'in_list[positive,neutral,negative]'
    ];
    
    protected $validationMessages = [
        'project_id' => [
            'required' => 'Project ID is required'
        ],
        'contractor_id' => [
            'required' => 'Contractor ID is required'
        ],
        'joined_at' => [
            'required' => 'Join date is required'
        ]
    ];
    
    /**
     * Get contractors by project
     */
    public function getByProject(int $projectId, bool $activeOnly = true): array
    {
        $query = $this->select('project_contractors.*, dakoii_organizations.name as contractor_name, dakoii_organizations.type as contractor_type')
                     ->join('dakoii_organizations', 'dakoii_organizations.id = project_contractors.contractor_id')
                     ->where('project_contractors.project_id', $projectId);
        
        if ($activeOnly) {
            $query = $query->where('project_contractors.is_active', 1);
        }
        
        return $query->orderBy('project_contractors.joined_at', 'ASC')->findAll();
    }
    
    /**
     * Get projects by contractor
     */
    public function getByContractor(int $contractorId, bool $activeOnly = true): array
    {
        $query = $this->select('project_contractors.*, projects.title as project_title, projects.pro_code, projects.status as project_status')
                     ->join('projects', 'projects.id = project_contractors.project_id')
                     ->where('project_contractors.contractor_id', $contractorId);
        
        if ($activeOnly) {
            $query = $query->where('project_contractors.is_active', 1);
        }
        
        return $query->orderBy('project_contractors.joined_at', 'DESC')->findAll();
    }
    
    /**
     * Get active contractors
     */
    public function getActiveContractors(): array
    {
        return $this->select('project_contractors.*, dakoii_organizations.name as contractor_name, projects.title as project_title')
                   ->join('dakoii_organizations', 'dakoii_organizations.id = project_contractors.contractor_id')
                   ->join('projects', 'projects.id = project_contractors.project_id')
                   ->where('project_contractors.is_active', 1)
                   ->orderBy('project_contractors.joined_at', 'DESC')
                   ->findAll();
    }
    
    /**
     * Add contractor to project
     */
    public function addContractor(int $projectId, int $contractorId, string $joinedAt, ?int $userId = null): bool
    {
        // Check if contractor is already assigned to this project
        $existing = $this->where('project_id', $projectId)
                        ->where('contractor_id', $contractorId)
                        ->where('is_active', 1)
                        ->first();
        
        if ($existing) {
            return false; // Already assigned
        }
        
        $data = [
            'project_id' => $projectId,
            'contractor_id' => $contractorId,
            'joined_at' => $joinedAt,
            'is_active' => 1,
            'client_flag' => 'neutral',
            'created_by' => $userId,
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        return $this->insert($data) !== false;
    }
    
    /**
     * Remove contractor from project
     */
    public function removeContractor(int $projectId, int $contractorId, string $reason, ?int $userId = null): bool
    {
        $assignment = $this->where('project_id', $projectId)
                          ->where('contractor_id', $contractorId)
                          ->where('is_active', 1)
                          ->first();
        
        if (!$assignment) {
            return false;
        }
        
        return $this->update($assignment['id'], [
            'is_active' => 0,
            'removal_reason' => $reason,
            'deleted_by' => $userId,
            'deleted_at' => date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * Update client flag
     */
    public function updateClientFlag(int $projectId, int $contractorId, string $flag): bool
    {
        $assignment = $this->where('project_id', $projectId)
                          ->where('contractor_id', $contractorId)
                          ->where('is_active', 1)
                          ->first();
        
        if (!$assignment) {
            return false;
        }
        
        return $this->update($assignment['id'], [
            'client_flag' => $flag
        ]);
    }
    
    /**
     * Get contractor statistics
     */
    public function getContractorStatistics(): array
    {
        $stats = [];
        
        // Active assignments
        $stats['active_assignments'] = $this->where('is_active', 1)->countAllResults();
        
        // Client flag distribution
        $flagCounts = $this->select('client_flag, COUNT(*) as count')
                          ->where('is_active', 1)
                          ->groupBy('client_flag')
                          ->findAll();
        
        $stats['by_client_flag'] = array_column($flagCounts, 'count', 'client_flag');
        
        // Most active contractors
        $stats['most_active'] = $this->select('contractor_id, COUNT(*) as project_count, dakoii_organizations.name as contractor_name')
                                    ->join('dakoii_organizations', 'dakoii_organizations.id = project_contractors.contractor_id')
                                    ->where('is_active', 1)
                                    ->groupBy('contractor_id')
                                    ->orderBy('project_count', 'DESC')
                                    ->limit(10)
                                    ->findAll();
        
        return $stats;
    }
    
    /**
     * Check if contractor is assigned to project
     */
    public function isContractorAssigned(int $projectId, int $contractorId): bool
    {
        return $this->where('project_id', $projectId)
                   ->where('contractor_id', $contractorId)
                   ->where('is_active', 1)
                   ->countAllResults() > 0;
    }
    
    /**
     * Get contractor assignment history
     */
    public function getAssignmentHistory(int $contractorId): array
    {
        return $this->select('project_contractors.*, projects.title as project_title, projects.pro_code')
                   ->join('projects', 'projects.id = project_contractors.project_id')
                   ->where('project_contractors.contractor_id', $contractorId)
                   ->orderBy('project_contractors.joined_at', 'DESC')
                   ->findAll();
    }
}
