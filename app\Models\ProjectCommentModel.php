<?php

namespace App\Models;

/**
 * Project Comment Model
 * 
 * Handles comments and discussions on projects and milestones.
 * Supports threaded conversations and comment management.
 */
class ProjectCommentModel extends BaseModel
{
    protected $table      = 'project_comments';
    protected $primaryKey = 'id';
    
    protected $allowedFields = [
        'project_id', 'milestone_id', 'parent_comment_id', 'comment_text',
        'comment_type', 'is_internal', 'created_by', 'updated_by', 'deleted_by'
    ];
    
    protected $useTimestamps = true;
    protected $useSoftDeletes = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';
    
    protected $validationRules = [
        'project_id'   => 'required|integer',
        'comment_text' => 'required|min_length[10]',
        'comment_type' => 'required|in_list[general,feedback,issue,approval,question]',
        'is_internal'  => 'in_list[0,1]'
    ];
    
    protected $validationMessages = [
        'project_id' => [
            'required' => 'Project ID is required'
        ],
        'comment_text' => [
            'required' => 'Comment text is required',
            'min_length' => 'Comment must be at least 10 characters long'
        ],
        'comment_type' => [
            'required' => 'Comment type is required'
        ]
    ];
    
    /**
     * Get comments by project
     */
    public function getByProject(int $projectId, bool $includeInternal = true): array
    {
        $query = $this->select('project_comments.*, admin_users.username, admin_users.first_name, admin_users.last_name')
                     ->join('admin_users', 'admin_users.id = project_comments.created_by')
                     ->where('project_comments.project_id', $projectId);
        
        if (!$includeInternal) {
            $query = $query->where('project_comments.is_internal', 0);
        }
        
        return $query->orderBy('project_comments.created_at', 'DESC')->findAll();
    }
    
    /**
     * Get comments by milestone
     */
    public function getByMilestone(int $milestoneId, bool $includeInternal = true): array
    {
        $query = $this->select('project_comments.*, admin_users.username, admin_users.first_name, admin_users.last_name')
                     ->join('admin_users', 'admin_users.id = project_comments.created_by')
                     ->where('project_comments.milestone_id', $milestoneId);
        
        if (!$includeInternal) {
            $query = $query->where('project_comments.is_internal', 0);
        }
        
        return $query->orderBy('project_comments.created_at', 'ASC')->findAll();
    }
    
    /**
     * Get comments by type
     */
    public function getByType(string $commentType, ?int $projectId = null): array
    {
        $query = $this->select('project_comments.*, admin_users.username, admin_users.first_name, admin_users.last_name, projects.title as project_title')
                     ->join('admin_users', 'admin_users.id = project_comments.created_by')
                     ->join('projects', 'projects.id = project_comments.project_id')
                     ->where('project_comments.comment_type', $commentType);
        
        if ($projectId) {
            $query = $query->where('project_comments.project_id', $projectId);
        }
        
        return $query->orderBy('project_comments.created_at', 'DESC')->findAll();
    }
    
    /**
     * Get threaded comments
     */
    public function getThreadedComments(int $projectId, ?int $milestoneId = null): array
    {
        $query = $this->select('project_comments.*, admin_users.username, admin_users.first_name, admin_users.last_name')
                     ->join('admin_users', 'admin_users.id = project_comments.created_by')
                     ->where('project_comments.project_id', $projectId)
                     ->where('project_comments.parent_comment_id IS NULL');
        
        if ($milestoneId) {
            $query = $query->where('project_comments.milestone_id', $milestoneId);
        }
        
        $parentComments = $query->orderBy('project_comments.created_at', 'ASC')->findAll();
        
        // Get replies for each parent comment
        foreach ($parentComments as &$comment) {
            $comment['replies'] = $this->getReplies($comment['id']);
        }
        
        return $parentComments;
    }
    
    /**
     * Get replies to a comment
     */
    public function getReplies(int $parentCommentId): array
    {
        return $this->select('project_comments.*, admin_users.username, admin_users.first_name, admin_users.last_name')
                   ->join('admin_users', 'admin_users.id = project_comments.created_by')
                   ->where('project_comments.parent_comment_id', $parentCommentId)
                   ->orderBy('project_comments.created_at', 'ASC')
                   ->findAll();
    }
    
    /**
     * Add comment
     */
    public function addComment(array $commentData): bool
    {
        $commentData['created_by'] = session()->get('admin_user_id');
        $commentData['created_at'] = date('Y-m-d H:i:s');
        
        return $this->insert($commentData) !== false;
    }
    
    /**
     * Reply to comment
     */
    public function replyToComment(int $parentCommentId, array $replyData): bool
    {
        $parentComment = $this->find($parentCommentId);
        
        if (!$parentComment) {
            return false;
        }
        
        $replyData['parent_comment_id'] = $parentCommentId;
        $replyData['project_id'] = $parentComment['project_id'];
        $replyData['milestone_id'] = $parentComment['milestone_id'];
        
        return $this->addComment($replyData);
    }
    
    /**
     * Update comment
     */
    public function updateComment(int $commentId, string $commentText, ?int $updatedBy = null): bool
    {
        return $this->update($commentId, [
            'comment_text' => $commentText,
            'updated_by' => $updatedBy,
            'updated_at' => date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * Get recent comments
     */
    public function getRecentComments(int $days = 7, ?int $projectId = null): array
    {
        $dateThreshold = date('Y-m-d', strtotime("-{$days} days"));
        
        $query = $this->select('project_comments.*, admin_users.username, admin_users.first_name, admin_users.last_name, projects.title as project_title')
                     ->join('admin_users', 'admin_users.id = project_comments.created_by')
                     ->join('projects', 'projects.id = project_comments.project_id')
                     ->where('project_comments.created_at >=', $dateThreshold);
        
        if ($projectId) {
            $query = $query->where('project_comments.project_id', $projectId);
        }
        
        return $query->orderBy('project_comments.created_at', 'DESC')->findAll();
    }
    
    /**
     * Get comment statistics
     */
    public function getCommentStatistics(?int $projectId = null): array
    {
        $stats = [];
        
        $query = $this;
        if ($projectId) {
            $query = $query->where('project_id', $projectId);
        }
        
        // Total comments
        $stats['total'] = $query->countAllResults();
        
        // Comments by type
        $typeCounts = $this->select('comment_type, COUNT(*) as count')
                          ->groupBy('comment_type')
                          ->findAll();
        
        $stats['by_type'] = array_column($typeCounts, 'count', 'comment_type');
        
        // Internal vs external comments
        $internalCounts = $this->select('is_internal, COUNT(*) as count')
                              ->groupBy('is_internal')
                              ->findAll();
        
        $stats['by_visibility'] = array_column($internalCounts, 'count', 'is_internal');
        
        // Recent comments (last 7 days)
        $recentDate = date('Y-m-d', strtotime('-7 days'));
        $recentQuery = $this->where('created_at >=', $recentDate);
        if ($projectId) {
            $recentQuery = $recentQuery->where('project_id', $projectId);
        }
        $stats['recent'] = $recentQuery->countAllResults();
        
        // Most active commenters
        $stats['most_active'] = $this->select('created_by, COUNT(*) as comment_count, admin_users.username, admin_users.first_name, admin_users.last_name')
                                    ->join('admin_users', 'admin_users.id = project_comments.created_by')
                                    ->groupBy('created_by')
                                    ->orderBy('comment_count', 'DESC')
                                    ->limit(10)
                                    ->findAll();
        
        return $stats;
    }
    
    /**
     * Get unresolved issues
     */
    public function getUnresolvedIssues(?int $projectId = null): array
    {
        $query = $this->select('project_comments.*, admin_users.username, admin_users.first_name, admin_users.last_name, projects.title as project_title')
                     ->join('admin_users', 'admin_users.id = project_comments.created_by')
                     ->join('projects', 'projects.id = project_comments.project_id')
                     ->where('project_comments.comment_type', 'issue');
        
        if ($projectId) {
            $query = $query->where('project_comments.project_id', $projectId);
        }
        
        return $query->orderBy('project_comments.created_at', 'DESC')->findAll();
    }
    
    /**
     * Mark comment as resolved (for issues)
     */
    public function markAsResolved(int $commentId, ?int $resolvedBy = null): bool
    {
        return $this->update($commentId, [
            'comment_type' => 'resolved_issue',
            'updated_by' => $resolvedBy,
            'updated_at' => date('Y-m-d H:i:s')
        ]);
    }
}
