<?php

namespace App\Models;

/**
 * Admin User Model
 * 
 * Handles admin portal users with role-based access control
 * and comprehensive user management features.
 */
class AdminUserModel extends BaseModel
{
    protected $table      = 'admin_users';
    protected $primaryKey = 'id';
    
    protected $allowedFields = [
        'username', 'email', 'password', 'first_name', 'last_name',
        'phone', 'role', 'status', 'last_login', 'login_attempts',
        'password_reset_token', 'password_reset_expires', 'email_verified_at',
        'created_by', 'updated_by', 'deleted_by'
    ];
    
    protected $useTimestamps = true;
    protected $useSoftDeletes = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';
    
    protected $beforeInsert = ['hashPassword'];
    protected $beforeUpdate = ['hashPassword'];
    
    protected $validationRules = [
        'username'   => 'required|min_length[3]|max_length[50]|is_unique[admin_users.username,id,{id}]',
        'email'      => 'required|valid_email|is_unique[admin_users.email,id,{id}]',
        'password'   => 'required|min_length[8]',
        'first_name' => 'required|max_length[50]',
        'last_name'  => 'required|max_length[50]',
        'role'       => 'required|in_list[super_admin,admin,project_manager,officer]',
        'status'     => 'in_list[active,inactive,suspended]'
    ];
    
    protected $validationMessages = [
        'username' => [
            'required' => 'Username is required',
            'is_unique' => 'Username already exists'
        ],
        'email' => [
            'required' => 'Email is required',
            'valid_email' => 'Please enter a valid email address',
            'is_unique' => 'Email already exists'
        ],
        'password' => [
            'required' => 'Password is required',
            'min_length' => 'Password must be at least 8 characters long'
        ]
    ];
    
    /**
     * Hash password before saving
     */
    protected function hashPassword(array $data): array
    {
        if (isset($data['data']['password'])) {
            $data['data']['password'] = password_hash($data['data']['password'], PASSWORD_DEFAULT);
        }
        
        return $data;
    }
    
    /**
     * Authenticate user
     */
    public function authenticate(string $username, string $password): ?array
    {
        $user = $this->where('username', $username)
                    ->orWhere('email', $username)
                    ->where('status', 'active')
                    ->first();
        
        if (!$user) {
            return null;
        }
        
        if (!password_verify($password, $user['password'])) {
            // Increment login attempts
            $this->incrementLoginAttempts($user['id']);
            return null;
        }
        
        // Reset login attempts and update last login
        $this->update($user['id'], [
            'last_login' => date('Y-m-d H:i:s'),
            'login_attempts' => 0
        ]);
        
        // Remove password from returned data
        unset($user['password']);
        
        return $user;
    }
    
    /**
     * Get users by role
     */
    public function getByRole(string $role, bool $activeOnly = true): array
    {
        $query = $this->where('role', $role);
        
        if ($activeOnly) {
            $query = $query->where('status', 'active');
        }
        
        return $query->orderBy('first_name', 'ASC')
                    ->orderBy('last_name', 'ASC')
                    ->findAll();
    }
    
    /**
     * Get active users
     */
    public function getActiveUsers(): array
    {
        return $this->where('status', 'active')
                   ->orderBy('first_name', 'ASC')
                   ->orderBy('last_name', 'ASC')
                   ->findAll();
    }
    
    /**
     * Search users
     */
    public function searchUsers(string $searchTerm): array
    {
        return $this->groupStart()
                   ->like('username', $searchTerm)
                   ->orLike('email', $searchTerm)
                   ->orLike('first_name', $searchTerm)
                   ->orLike('last_name', $searchTerm)
                   ->groupEnd()
                   ->where('status', 'active')
                   ->orderBy('first_name', 'ASC')
                   ->findAll();
    }
    
    /**
     * Update user status
     */
    public function updateStatus(int $userId, string $status, ?int $updatedBy = null): bool
    {
        return $this->update($userId, [
            'status' => $status,
            'updated_by' => $updatedBy
        ]);
    }
    
    /**
     * Change password
     */
    public function changePassword(int $userId, string $newPassword, ?int $updatedBy = null): bool
    {
        return $this->update($userId, [
            'password' => password_hash($newPassword, PASSWORD_DEFAULT),
            'password_reset_token' => null,
            'password_reset_expires' => null,
            'updated_by' => $updatedBy
        ]);
    }
    
    /**
     * Generate password reset token
     */
    public function generatePasswordResetToken(string $email): ?string
    {
        $user = $this->where('email', $email)
                    ->where('status', 'active')
                    ->first();
        
        if (!$user) {
            return null;
        }
        
        $token = bin2hex(random_bytes(32));
        $expires = date('Y-m-d H:i:s', strtotime('+1 hour'));
        
        $this->update($user['id'], [
            'password_reset_token' => $token,
            'password_reset_expires' => $expires
        ]);
        
        return $token;
    }
    
    /**
     * Verify password reset token
     */
    public function verifyPasswordResetToken(string $token): ?array
    {
        return $this->where('password_reset_token', $token)
                   ->where('password_reset_expires >', date('Y-m-d H:i:s'))
                   ->where('status', 'active')
                   ->first();
    }
    
    /**
     * Increment login attempts
     */
    protected function incrementLoginAttempts(int $userId): void
    {
        $user = $this->find($userId);
        $attempts = ($user['login_attempts'] ?? 0) + 1;
        
        $updateData = ['login_attempts' => $attempts];
        
        // Suspend user after 5 failed attempts
        if ($attempts >= 5) {
            $updateData['status'] = 'suspended';
        }
        
        $this->update($userId, $updateData);
    }
    
    /**
     * Get user statistics
     */
    public function getUserStatistics(): array
    {
        $stats = [];
        
        // Count by status
        $statusCounts = $this->select('status, COUNT(*) as count')
                            ->groupBy('status')
                            ->findAll();
        
        $stats['by_status'] = array_column($statusCounts, 'count', 'status');
        
        // Count by role
        $roleCounts = $this->select('role, COUNT(*) as count')
                          ->where('status', 'active')
                          ->groupBy('role')
                          ->findAll();
        
        $stats['by_role'] = array_column($roleCounts, 'count', 'role');
        
        // Recent logins (last 30 days)
        $recentDate = date('Y-m-d', strtotime('-30 days'));
        $stats['recent_logins'] = $this->where('last_login >=', $recentDate)
                                      ->countAllResults();
        
        // Total users
        $stats['total'] = $this->countAllResults();
        
        return $stats;
    }
    
    /**
     * Get full name
     */
    public function getFullName(array $user): string
    {
        return trim($user['first_name'] . ' ' . $user['last_name']);
    }
    
    /**
     * Check if user has permission
     */
    public function hasPermission(array $user, string $permission): bool
    {
        $rolePermissions = [
            'super_admin' => ['*'], // All permissions
            'admin' => [
                'projects.view', 'projects.create', 'projects.edit', 'projects.delete',
                'users.view', 'users.create', 'users.edit',
                'reports.view', 'audit.view'
            ],
            'project_manager' => [
                'projects.view', 'projects.create', 'projects.edit',
                'milestones.view', 'milestones.create', 'milestones.edit',
                'evidence.view', 'evidence.upload'
            ],
            'officer' => [
                'projects.view', 'milestones.view', 'evidence.view'
            ]
        ];
        
        $userPermissions = $rolePermissions[$user['role']] ?? [];
        
        return in_array('*', $userPermissions) || in_array($permission, $userPermissions);
    }
}
