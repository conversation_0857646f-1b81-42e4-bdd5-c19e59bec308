<?php

// Simple script to run the database seeder
require_once 'vendor/autoload.php';

// Load CodeIgniter
$app = \Config\Services::codeigniter();
$app->initialize();

// Run the seeder
$seeder = \Config\Database::seeder();
$seeder->call('DatabaseSeeder');

echo "Database seeding completed!\n\n";
echo "=== DAKOII PORTAL ADMIN ===\n";
echo "Username: admin\n";
echo "Password: admin123\n";
echo "Email: <EMAIL>\n\n";

echo "=== ORGANIZATION USERS ===\n";
echo "Sample Organization Admin:\n";
echo "  Username: sampleadmin\n";
echo "  Password: admin123\n";
echo "  Email: <EMAIL>\n\n";

echo "Sample Organization Moderator:\n";
echo "  Username: samplemod\n";
echo "  Password: mod123\n";
echo "  Email: <EMAIL>\n\n";

echo "Test Organization Admin (Pending):\n";
echo "  Username: testadmin\n";
echo "  Password: test123\n";
echo "  Email: <EMAIL>\n\n";

echo "Test Organization User:\n";
echo "  Username: testuser\n";
echo "  Password: user123\n";
echo "  Email: <EMAIL>\n\n";

echo "Access the portal at: http://localhost/promis_two/dakoii\n";
