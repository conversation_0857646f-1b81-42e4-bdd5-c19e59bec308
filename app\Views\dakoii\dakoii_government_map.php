<?= $this->extend('templates/dakoii_portal_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('dakoii/government') ?>" class="btn btn-secondary">
    <i class="icon">←</i> Back to Overview
</a>
<button class="btn btn-primary" onclick="exportMap()">
    <i class="icon">📤</i> Export Map
</button>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Page Header -->
    <div style="margin-bottom: var(--spacing-xl);">
        <h1 style="color: var(--text-primary); margin-bottom: var(--spacing-sm); font-size: 2rem; display: flex; align-items: center; gap: var(--spacing-md);">
            <span style="font-size: 2.5rem;">🗺️</span>
            Geographic Map View
        </h1>
        <p style="color: var(--text-secondary); font-size: 1rem;">
            Interactive map visualization of government administrative boundaries and locations.
        </p>
    </div>

    <!-- Map Controls -->
    <div class="card" style="margin-bottom: var(--spacing-xl);">
        <div class="card-header">Map Controls</div>
        <div style="display: flex; gap: var(--spacing-md); align-items: center; flex-wrap: wrap;">
            <div style="display: flex; align-items: center; gap: var(--spacing-sm);">
                <label class="form-label" style="margin: 0;">Show Layer:</label>
                <select id="mapLayer" class="form-input" style="width: auto;" onchange="updateMapLayer()">
                    <option value="all">All Boundaries</option>
                    <option value="countries">Countries</option>
                    <option value="provinces">Provinces</option>
                    <option value="districts">Districts</option>
                    <option value="llgs">LLGs</option>
                </select>
            </div>
            <div style="display: flex; align-items: center; gap: var(--spacing-sm);">
                <label class="form-label" style="margin: 0;">Map Type:</label>
                <select id="mapType" class="form-input" style="width: auto;" onchange="updateMapType()">
                    <option value="satellite">Satellite</option>
                    <option value="terrain">Terrain</option>
                    <option value="roadmap">Roadmap</option>
                    <option value="hybrid">Hybrid</option>
                </select>
            </div>
            <button class="btn btn-secondary" onclick="resetMapView()">
                <i class="icon">🎯</i> Reset View
            </button>
            <button class="btn btn-secondary" onclick="toggleLabels()">
                <i class="icon">🏷️</i> Toggle Labels
            </button>
            <button class="btn btn-secondary" onclick="measureDistance()">
                <i class="icon">📏</i> Measure
            </button>
        </div>
    </div>

    <!-- Interactive Map -->
    <div class="card">
        <div class="card-header">Interactive Government Map</div>
        <div id="government-map" style="height: 600px; position: relative; border-radius: var(--radius-md); overflow: hidden;">
            <!-- Map will be rendered here -->
            <div style="display: flex; align-items: center; justify-content: center; height: 100%; background: linear-gradient(135deg, #1a1a2e, #16213e); color: var(--text-tertiary);">
                <div style="text-align: center;">
                    <div style="font-size: 4rem; margin-bottom: var(--spacing-md);">🗺️</div>
                    <h3 style="color: var(--text-primary); margin-bottom: var(--spacing-sm);">Loading Interactive Map...</h3>
                    <p>Government administrative boundaries will be displayed here.</p>
                    <div style="margin-top: var(--spacing-lg);">
                        <div class="loading-spinner" style="width: 40px; height: 40px; border: 3px solid rgba(255, 255, 255, 0.1); border-top: 3px solid var(--primary); border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Map Information Panel -->
    <div style="display: grid; grid-template-columns: 2fr 1fr; gap: var(--spacing-xl); margin-top: var(--spacing-xl);">
        <!-- Map Statistics -->
        <div class="card">
            <div class="card-header">Map Statistics</div>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: var(--spacing-md);">
                <div style="text-align: center; padding: var(--spacing-md);">
                    <div style="font-size: 2rem; font-weight: 700; color: var(--primary);" id="countriesCount">0</div>
                    <div style="color: var(--text-secondary); font-size: 0.875rem;">Countries</div>
                </div>
                <div style="text-align: center; padding: var(--spacing-md);">
                    <div style="font-size: 2rem; font-weight: 700; color: var(--secondary);" id="provincesCount">0</div>
                    <div style="color: var(--text-secondary); font-size: 0.875rem;">Provinces</div>
                </div>
                <div style="text-align: center; padding: var(--spacing-md);">
                    <div style="font-size: 2rem; font-weight: 700; color: var(--accent);" id="districtsCount">0</div>
                    <div style="color: var(--text-secondary); font-size: 0.875rem;">Districts</div>
                </div>
                <div style="text-align: center; padding: var(--spacing-md);">
                    <div style="font-size: 2rem; font-weight: 700; color: #06FFA5;" id="llgsCount">0</div>
                    <div style="color: var(--text-secondary); font-size: 0.875rem;">LLGs</div>
                </div>
            </div>
        </div>

        <!-- Map Legend -->
        <div class="card">
            <div class="card-header">Map Legend</div>
            <div style="display: flex; flex-direction: column; gap: var(--spacing-sm);">
                <div style="display: flex; align-items: center; gap: var(--spacing-sm);">
                    <div style="width: 20px; height: 4px; background: var(--gradient-primary);"></div>
                    <span style="color: var(--text-secondary); font-size: 0.875rem;">Country Boundaries</span>
                </div>
                <div style="display: flex; align-items: center; gap: var(--spacing-sm);">
                    <div style="width: 20px; height: 4px; background: var(--gradient-secondary);"></div>
                    <span style="color: var(--text-secondary); font-size: 0.875rem;">Province Boundaries</span>
                </div>
                <div style="display: flex; align-items: center; gap: var(--spacing-sm);">
                    <div style="width: 20px; height: 4px; background: var(--gradient-accent);"></div>
                    <span style="color: var(--text-secondary); font-size: 0.875rem;">District Boundaries</span>
                </div>
                <div style="display: flex; align-items: center; gap: var(--spacing-sm);">
                    <div style="width: 20px; height: 4px; background: #06FFA5;"></div>
                    <span style="color: var(--text-secondary); font-size: 0.875rem;">LLG Boundaries</span>
                </div>
                <div style="display: flex; align-items: center; gap: var(--spacing-sm); margin-top: var(--spacing-md);">
                    <div style="width: 20px; height: 20px; background: rgba(255, 0, 110, 0.3); border: 2px solid #FF006E; border-radius: 50%;"></div>
                    <span style="color: var(--text-secondary); font-size: 0.875rem;">Administrative Centers</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Selected Area Information -->
    <div id="selectedAreaInfo" class="card" style="margin-top: var(--spacing-xl); display: none;">
        <div class="card-header">Selected Area Information</div>
        <div id="areaDetails">
            <!-- Selected area details will be populated here -->
        </div>
    </div>
</div>

<style>
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.map-popup {
    background: var(--surface-card);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    color: var(--text-primary);
    font-size: 0.875rem;
    max-width: 250px;
}

.map-popup h4 {
    margin: 0 0 var(--spacing-xs) 0;
    color: var(--text-primary);
    font-size: 1rem;
}

.map-popup p {
    margin: 0;
    color: var(--text-secondary);
}
</style>

<script>
// Government Map Implementation
let map = null;
let currentLayer = 'all';
let currentMapType = 'satellite';
let showLabels = true;
let measureMode = false;

document.addEventListener('DOMContentLoaded', function() {
    initializeMap();
    loadMapData();
});

function initializeMap() {
    // Initialize map (using a placeholder implementation)
    // In a real implementation, this would use Google Maps, Leaflet, or similar

    const mapContainer = document.getElementById('government-map');

    // Simulate map loading
    setTimeout(() => {
        mapContainer.innerHTML = `
            <div style="height: 100%; background: linear-gradient(135deg, #2c5530, #1a3d1f); position: relative; display: flex; align-items: center; justify-content: center;">
                <div style="text-align: center; color: white;">
                    <div style="font-size: 3rem; margin-bottom: var(--spacing-md);">🗺️</div>
                    <h3 style="margin-bottom: var(--spacing-sm);">Interactive Map Loaded</h3>
                    <p style="opacity: 0.8;">Click on administrative boundaries to view details</p>
                    <div style="position: absolute; top: 20px; left: 20px; background: rgba(0,0,0,0.7); padding: var(--spacing-sm); border-radius: var(--radius-sm);">
                        <div style="font-size: 0.875rem;">Papua New Guinea</div>
                        <div style="font-size: 0.75rem; opacity: 0.8;">Government Administrative Map</div>
                    </div>
                    <div style="position: absolute; bottom: 20px; right: 20px; background: rgba(0,0,0,0.7); padding: var(--spacing-sm); border-radius: var(--radius-sm);">
                        <div style="font-size: 0.75rem;">Zoom: 6 | Layer: ${currentLayer}</div>
                    </div>
                </div>
            </div>
        `;

        // Update statistics
        updateMapStatistics();
    }, 2000);
}

function loadMapData() {
    // Simulate loading map data from server
    // In a real implementation, this would fetch GeoJSON data
    console.log('Loading government boundary data...');
}

function updateMapLayer() {
    const layer = document.getElementById('mapLayer').value;
    currentLayer = layer;

    console.log('Updating map layer to:', layer);
    // Implementation would update the visible map layers

    // Update the map display
    const mapContainer = document.getElementById('government-map');
    const layerInfo = mapContainer.querySelector('[style*="bottom: 20px"]');
    if (layerInfo) {
        layerInfo.innerHTML = `<div style="font-size: 0.75rem;">Zoom: 6 | Layer: ${layer}</div>`;
    }
}

function updateMapType() {
    const mapType = document.getElementById('mapType').value;
    currentMapType = mapType;

    console.log('Updating map type to:', mapType);
    // Implementation would change the base map layer
}

function resetMapView() {
    console.log('Resetting map view to default bounds');
    // Implementation would reset map to default zoom and center
}

function toggleLabels() {
    showLabels = !showLabels;
    console.log('Labels:', showLabels ? 'shown' : 'hidden');
    // Implementation would toggle administrative labels
}

function measureDistance() {
    measureMode = !measureMode;
    console.log('Measure mode:', measureMode ? 'enabled' : 'disabled');
    // Implementation would enable distance measurement tool
}

function exportMap() {
    console.log('Exporting map...');
    // Implementation would export map as image
    alert('Map export functionality will be implemented.');
}

function updateMapStatistics() {
    // Simulate updating statistics
    document.getElementById('countriesCount').textContent = '1';
    document.getElementById('provincesCount').textContent = '22';
    document.getElementById('districtsCount').textContent = '89';
    document.getElementById('llgsCount').textContent = '326';
}

function showAreaInfo(areaData) {
    const infoPanel = document.getElementById('selectedAreaInfo');
    const detailsDiv = document.getElementById('areaDetails');

    detailsDiv.innerHTML = `
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: var(--spacing-md);">
            <div>
                <h4 style="margin: 0 0 var(--spacing-xs) 0; color: var(--text-primary);">${areaData.name}</h4>
                <p style="margin: 0; color: var(--text-secondary);">Type: ${areaData.type}</p>
                <p style="margin: 0; color: var(--text-secondary);">Code: ${areaData.code || 'N/A'}</p>
            </div>
            <div>
                <p style="margin: 0; color: var(--text-secondary);">Area: ${areaData.area || 'N/A'} km²</p>
                <p style="margin: 0; color: var(--text-secondary);">Population: ${areaData.population || 'N/A'}</p>
                <p style="margin: 0; color: var(--text-secondary);">Established: ${areaData.established || 'N/A'}</p>
            </div>
        </div>
    `;

    infoPanel.style.display = 'block';
}
</script>
<?= $this->endSection() ?>