<?= $this->extend('templates/dakoii_portal_template') ?>
<?= $this->section('content') ?>
<h1>District Profile</h1>
<?php if (isset($district)): ?>
    <ul>
        <li><strong>Name:</strong> <?= esc($district['name']) ?></li>
        <li><strong>District Code:</strong> <?= esc($district['dist_code']) ?></li>
        <li><strong>Province ID:</strong> <?= esc($district['province_id']) ?></li>
    </ul>
    <a class="btn btn-secondary" href="<?= base_url('dakoii/government/districts/'.$district['id'].'/edit') ?>">Edit</a>
    <form action="<?= base_url('dakoii/government/districts/'.$district['id'].'/delete') ?>" method="post" style="display:inline;">
        <?= csrf_field() ?>
        <button type="submit" onclick="return confirm('Delete this district?')">Delete</button>
    </form>
<?php else: ?>
    <p>District not found.</p>
<?php endif; ?>
<?= $this->endSection() ?> 