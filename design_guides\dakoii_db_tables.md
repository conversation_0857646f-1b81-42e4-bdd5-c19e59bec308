/* ================================================================
   1) Super-admin users (Dakoii Portal)
   ================================================================ */
CREATE TABLE dakoii_users (
    id                   BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,

    /* Business identifiers */
    user_code            VARCHAR(12)  NOT NULL UNIQUE,
    username             VARCHAR(50)  NOT NULL UNIQUE,
    email                VARCHAR(100) NOT NULL UNIQUE,

    /* Profile */
    name                 VARCHAR(100) NOT NULL,
    role                 ENUM('admin','moderator','user')
                                NOT NULL DEFAULT 'user',
    id_photo_path        VARCHAR(255) NULL,

    /* Security & lifecycle */
    password_hash        VARCHAR(255) NOT NULL,         -- Argon2id hash  
    activation_token     CHAR(64)     NULL,
    is_activated         TINYINT(1)   NOT NULL DEFAULT 0,
    password_reset_token CHAR(64)     NULL,
    last_login_at        DATETIME     NULL,

    /* Timestamps */
    created_at           DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at           DATETIME     NULL ON UPDATE CURRENT_TIMESTAMP,
    deleted_at           DATETIME     NULL,

    /* NEW: audit-by columns (no FK constraint) */
    created_by           BIGINT UNSIGNED NULL,
    updated_by           BIGINT UNSIGNED NULL,
    deleted_by           BIGINT UNSIGNED NULL,

    /* Handy indexes */
    INDEX idx_email            (email),
    INDEX idx_activation       (activation_token),
    INDEX idx_password_reset   (password_reset_token),
    INDEX idx_soft_delete      (deleted_at)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE      = utf8mb4_unicode_ci;

/* ================================================================
   2) Organisations  (FKs removed, location locks + audit-by columns)
   ================================================================ */
CREATE TABLE organizations (
    id                   BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,

    /* Business identifiers */
    org_code             CHAR(5)       NOT NULL UNIQUE,
    name                 VARCHAR(150)  NOT NULL,
    description          TEXT          NULL,

    /* Status & licence */
    license_status       ENUM('paid','unpaid')
                                  NOT NULL DEFAULT 'paid',
    is_active            TINYINT(1)    NOT NULL DEFAULT 1,

    /* Branding */
    logo_path            VARCHAR(255)  NULL,
    wallpaper_path       VARCHAR(255)  NULL,

    /* Contact & address */
    contact_email        VARCHAR(100)  NULL,
    contact_phone        VARCHAR(30)   NULL,
    address_line1        VARCHAR(150)  NULL,
    address_line2        VARCHAR(150)  NULL,
    city                 VARCHAR(100)  NULL,
    state                VARCHAR(100)  NULL,
    postal_code          VARCHAR(20)   NULL,
    country              VARCHAR(100)  NULL,

    /* Headquarters GPS */
    hq_lat               DECIMAL(10,8) NULL,
    hq_lng               DECIMAL(11,8) NULL,

    /* Web & social */
    website_url          VARCHAR(150)  NULL,
    facebook_url         VARCHAR(150)  NULL,
    twitter_url          VARCHAR(150)  NULL,
    linkedin_url         VARCHAR(150)  NULL,
    instagram_url        VARCHAR(150)  NULL,

    /* Location locks */
    country_lock         VARCHAR(20)   NULL,
    province_lock        VARCHAR(20)   NULL,
    district_lock        VARCHAR(20)   NULL,
    llg                  VARCHAR(20)   NULL,

    /* Timestamps */
    created_at           DATETIME      NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at           DATETIME      NULL ON UPDATE CURRENT_TIMESTAMP,
    deleted_at           DATETIME      NULL,

    /* NEW: audit-by columns */
    created_by           BIGINT UNSIGNED NULL,
    updated_by           BIGINT UNSIGNED NULL,
    deleted_by           BIGINT UNSIGNED NULL,

    /* Indexes */
    INDEX idx_license      (license_status),
    INDEX idx_active       (is_active),
    INDEX idx_soft_delete  (deleted_at)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE      = utf8mb4_unicode_ci;

/* ================================================================
   3) Organisation images  (FK removed, audit-by columns added)
   ================================================================ */
CREATE TABLE organization_images (
    id                   BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    organization_id      BIGINT UNSIGNED NOT NULL,   -- plain field; enforce in code
    image_path           VARCHAR(255)    NOT NULL,
    caption              VARCHAR(150)    NULL,
    sort_order           TINYINT UNSIGNED NULL,      -- 1-5 display order

    /* Timestamps */
    created_at           DATETIME        NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at           DATETIME        NULL,

    /* NEW: audit-by columns */
    created_by           BIGINT UNSIGNED NULL,
    updated_by           BIGINT UNSIGNED NULL,
    deleted_by           BIGINT UNSIGNED NULL,

    /* Indexes */
    INDEX idx_org         (organization_id),
    INDEX idx_soft_delete (deleted_at)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE      = utf8mb4_unicode_ci;



/* ================================================================
   1) Countries
   ================================================================ */
CREATE TABLE countries (
    id               BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    iso2             CHAR(2)      NOT NULL UNIQUE,
    iso3             CHAR(3)      NOT NULL UNIQUE,
    name             VARCHAR(100) NOT NULL,

    /* Map defaults */
    map_centre_gps   POINT        NULL SRID 4326,   -- default centre
    map_zoom         TINYINT UNSIGNED NULL,         -- default zoom level

    /* Timestamps */
    created_at       DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at       DATETIME     NULL ON UPDATE CURRENT_TIMESTAMP,
    deleted_at       DATETIME     NULL,

    /* Audit */
    created_by       BIGINT UNSIGNED NULL,
    updated_by       BIGINT UNSIGNED NULL,
    deleted_by       BIGINT UNSIGNED NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE      = utf8mb4_unicode_ci;

/* ================================================================
   2) Provinces / States
   ================================================================ */
CREATE TABLE provinces (
    id               BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    country_id       BIGINT UNSIGNED NOT NULL,            -- parent (no FK)
    prov_code        VARCHAR(10)    NOT NULL,
    name             VARCHAR(100)   NOT NULL,

    /* Map defaults */
    map_centre_gps   POINT          NULL SRID 4326,
    map_zoom         TINYINT UNSIGNED NULL,

    created_at       DATETIME       NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at       DATETIME       NULL ON UPDATE CURRENT_TIMESTAMP,
    deleted_at       DATETIME       NULL,

    created_by       BIGINT UNSIGNED NULL,
    updated_by       BIGINT UNSIGNED NULL,
    deleted_by       BIGINT UNSIGNED NULL,

    UNIQUE KEY uk_country_code (country_id, prov_code),
    INDEX idx_country      (country_id),
    INDEX idx_soft_delete  (deleted_at)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE      = utf8mb4_unicode_ci;

/* ================================================================
   3) Districts
   ================================================================ */
CREATE TABLE districts (
    id               BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    province_id      BIGINT UNSIGNED NOT NULL,            -- parent (no FK)
    dist_code        VARCHAR(10)    NOT NULL,
    name             VARCHAR(100)   NOT NULL,

    /* Map defaults */
    map_centre_gps   POINT          NULL SRID 4326,
    map_zoom         TINYINT UNSIGNED NULL,

    created_at       DATETIME       NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at       DATETIME       NULL ON UPDATE CURRENT_TIMESTAMP,
    deleted_at       DATETIME       NULL,

    created_by       BIGINT UNSIGNED NULL,
    updated_by       BIGINT UNSIGNED NULL,
    deleted_by       BIGINT UNSIGNED NULL,

    UNIQUE KEY uk_province_code (province_id, dist_code),
    INDEX idx_province     (province_id),
    INDEX idx_soft_delete  (deleted_at)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE      = utf8mb4_unicode_ci;

/* ================================================================
   4) Local-Level Governments (LLGs)
   ================================================================ */
CREATE TABLE llgs (
    id               BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    district_id      BIGINT UNSIGNED NOT NULL,            -- parent (no FK)
    llg_code         VARCHAR(10)    NOT NULL,
    name             VARCHAR(100)   NOT NULL,

    /* Map defaults */
    map_centre_gps   POINT          NULL SRID 4326,
    map_zoom         TINYINT UNSIGNED NULL,

    created_at       DATETIME       NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at       DATETIME       NULL ON UPDATE CURRENT_TIMESTAMP,
    deleted_at       DATETIME       NULL,

    created_by       BIGINT UNSIGNED NULL,
    updated_by       BIGINT UNSIGNED NULL,
    deleted_by       BIGINT UNSIGNED NULL,

    UNIQUE KEY uk_district_code (district_id, llg_code),
    INDEX idx_district     (district_id),
    INDEX idx_soft_delete  (deleted_at)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE      = utf8mb4_unicode_ci;
