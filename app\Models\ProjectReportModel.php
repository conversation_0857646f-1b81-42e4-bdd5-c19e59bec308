<?php

namespace App\Models;

/**
 * Project Report Model
 * 
 * Handles project reporting with various report types and scheduling.
 * Supports automated report generation and distribution.
 */
class ProjectReportModel extends BaseModel
{
    protected $table      = 'project_reports';
    protected $primaryKey = 'id';
    
    protected $allowedFields = [
        'project_id', 'report_type', 'title', 'content', 'file_path',
        'report_date', 'period_start', 'period_end', 'status',
        'generated_by', 'approved_by', 'approved_at', 'created_by', 'updated_by', 'deleted_by'
    ];
    
    protected $useTimestamps = true;
    protected $useSoftDeletes = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';
    
    protected $validationRules = [
        'project_id'   => 'required|integer',
        'report_type'  => 'required|in_list[progress,financial,milestone,final,custom]',
        'title'        => 'required|max_length[200]',
        'report_date'  => 'required|valid_date',
        'status'       => 'in_list[draft,pending,approved,published]'
    ];
    
    protected $validationMessages = [
        'project_id' => [
            'required' => 'Project ID is required'
        ],
        'report_type' => [
            'required' => 'Report type is required'
        ],
        'title' => [
            'required' => 'Report title is required'
        ],
        'report_date' => [
            'required' => 'Report date is required'
        ]
    ];
    
    /**
     * Get reports by project
     */
    public function getByProject(int $projectId, ?string $reportType = null): array
    {
        $query = $this->where('project_id', $projectId);
        
        if ($reportType) {
            $query = $query->where('report_type', $reportType);
        }
        
        return $query->orderBy('report_date', 'DESC')->findAll();
    }
    
    /**
     * Get reports by type
     */
    public function getByType(string $reportType): array
    {
        return $this->select('project_reports.*, projects.title as project_title, projects.pro_code')
                   ->join('projects', 'projects.id = project_reports.project_id')
                   ->where('project_reports.report_type', $reportType)
                   ->orderBy('project_reports.report_date', 'DESC')
                   ->findAll();
    }
    
    /**
     * Get reports by status
     */
    public function getByStatus(string $status): array
    {
        return $this->select('project_reports.*, projects.title as project_title, projects.pro_code')
                   ->join('projects', 'projects.id = project_reports.project_id')
                   ->where('project_reports.status', $status)
                   ->orderBy('project_reports.created_at', 'DESC')
                   ->findAll();
    }
    
    /**
     * Get pending reports
     */
    public function getPendingReports(): array
    {
        return $this->getByStatus('pending');
    }
    
    /**
     * Get reports for approval
     */
    public function getReportsForApproval(): array
    {
        return $this->select('project_reports.*, projects.title as project_title, projects.pro_code, admin_users.username as generated_by_username')
                   ->join('projects', 'projects.id = project_reports.project_id')
                   ->join('admin_users', 'admin_users.id = project_reports.generated_by')
                   ->where('project_reports.status', 'pending')
                   ->orderBy('project_reports.created_at', 'ASC')
                   ->findAll();
    }
    
    /**
     * Generate report
     */
    public function generateReport(array $reportData): bool
    {
        $reportData['status'] = 'draft';
        $reportData['generated_by'] = session()->get('admin_user_id');
        $reportData['created_by'] = session()->get('admin_user_id');
        $reportData['created_at'] = date('Y-m-d H:i:s');
        
        return $this->insert($reportData) !== false;
    }
    
    /**
     * Submit report for approval
     */
    public function submitForApproval(int $reportId, ?int $submittedBy = null): bool
    {
        return $this->update($reportId, [
            'status' => 'pending',
            'updated_by' => $submittedBy,
            'updated_at' => date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * Approve report
     */
    public function approveReport(int $reportId, int $approvedBy): bool
    {
        return $this->update($reportId, [
            'status' => 'approved',
            'approved_by' => $approvedBy,
            'approved_at' => date('Y-m-d H:i:s'),
            'updated_by' => $approvedBy,
            'updated_at' => date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * Publish report
     */
    public function publishReport(int $reportId, ?int $publishedBy = null): bool
    {
        return $this->update($reportId, [
            'status' => 'published',
            'updated_by' => $publishedBy,
            'updated_at' => date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * Reject report
     */
    public function rejectReport(int $reportId, string $reason, int $rejectedBy): bool
    {
        $report = $this->find($reportId);
        
        if (!$report) {
            return false;
        }
        
        $content = $report['content'] . "\n\n--- REJECTION REASON ---\n" . $reason;
        
        return $this->update($reportId, [
            'status' => 'draft',
            'content' => $content,
            'updated_by' => $rejectedBy,
            'updated_at' => date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * Get report statistics
     */
    public function getReportStatistics(?int $projectId = null): array
    {
        $stats = [];
        
        $query = $this;
        if ($projectId) {
            $query = $query->where('project_id', $projectId);
        }
        
        // Total reports
        $stats['total'] = $query->countAllResults();
        
        // Reports by type
        $typeCounts = $this->select('report_type, COUNT(*) as count')
                          ->groupBy('report_type')
                          ->findAll();
        
        $stats['by_type'] = array_column($typeCounts, 'count', 'report_type');
        
        // Reports by status
        $statusCounts = $this->select('status, COUNT(*) as count')
                            ->groupBy('status')
                            ->findAll();
        
        $stats['by_status'] = array_column($statusCounts, 'count', 'status');
        
        // Recent reports (last 30 days)
        $recentDate = date('Y-m-d', strtotime('-30 days'));
        $recentQuery = $this->where('created_at >=', $recentDate);
        if ($projectId) {
            $recentQuery = $recentQuery->where('project_id', $projectId);
        }
        $stats['recent'] = $recentQuery->countAllResults();
        
        return $stats;
    }
    
    /**
     * Get overdue reports
     */
    public function getOverdueReports(): array
    {
        $overdueDate = date('Y-m-d', strtotime('-7 days'));
        
        return $this->select('project_reports.*, projects.title as project_title, projects.pro_code')
                   ->join('projects', 'projects.id = project_reports.project_id')
                   ->where('project_reports.status', 'draft')
                   ->where('project_reports.created_at <', $overdueDate)
                   ->orderBy('project_reports.created_at', 'ASC')
                   ->findAll();
    }
    
    /**
     * Get reports by date range
     */
    public function getByDateRange(string $startDate, string $endDate, ?int $projectId = null): array
    {
        $query = $this->select('project_reports.*, projects.title as project_title, projects.pro_code')
                     ->join('projects', 'projects.id = project_reports.project_id')
                     ->where('project_reports.report_date >=', $startDate)
                     ->where('project_reports.report_date <=', $endDate);
        
        if ($projectId) {
            $query = $query->where('project_reports.project_id', $projectId);
        }
        
        return $query->orderBy('project_reports.report_date', 'DESC')->findAll();
    }
    
    /**
     * Get latest report by type for project
     */
    public function getLatestByType(int $projectId, string $reportType): ?array
    {
        return $this->where('project_id', $projectId)
                   ->where('report_type', $reportType)
                   ->where('status', 'published')
                   ->orderBy('report_date', 'DESC')
                   ->first();
    }
    
    /**
     * Check if report exists for period
     */
    public function reportExistsForPeriod(int $projectId, string $reportType, string $periodStart, string $periodEnd): bool
    {
        return $this->where('project_id', $projectId)
                   ->where('report_type', $reportType)
                   ->where('period_start', $periodStart)
                   ->where('period_end', $periodEnd)
                   ->countAllResults() > 0;
    }
    
    /**
     * Get report approval workflow
     */
    public function getApprovalWorkflow(int $reportId): array
    {
        $report = $this->select('project_reports.*, projects.title as project_title, 
                                generated_user.username as generated_by_username,
                                approved_user.username as approved_by_username')
                      ->join('projects', 'projects.id = project_reports.project_id')
                      ->join('admin_users as generated_user', 'generated_user.id = project_reports.generated_by', 'left')
                      ->join('admin_users as approved_user', 'approved_user.id = project_reports.approved_by', 'left')
                      ->where('project_reports.id', $reportId)
                      ->first();
        
        return $report ?: [];
    }
}
