<?= $this->extend('templates/dakoii_portal_template') ?>
<?= $this->section('content') ?>
<h1>Province Profile</h1>
<?php if (isset($province)): ?>
    <ul>
        <li><strong>Name:</strong> <?= esc($province['name']) ?></li>
        <li><strong>Province Code:</strong> <?= esc($province['prov_code']) ?></li>
        <li><strong>Country ID:</strong> <?= esc($province['country_id']) ?></li>
    </ul>
    <a class="btn btn-secondary" href="<?= base_url('dakoii/government/provinces/'.$province['id'].'/edit') ?>">Edit</a>
    <form action="<?= base_url('dakoii/government/provinces/'.$province['id'].'/delete') ?>" method="post" style="display:inline;">
        <?= csrf_field() ?>
        <button type="submit" onclick="return confirm('Delete this province?')">Delete</button>
    </form>
<?php else: ?>
    <p>Province not found.</p>
<?php endif; ?>
<?= $this->endSection() ?> 