<?php

namespace App\Models;

use CodeIgniter\Model;

/**
 * Model for the 'organization_images' table.
 * Represents images associated with organizations, including audit-by columns.
 */
class OrganizationImageModel extends Model
{
    /**
     * @var string The database table this model represents.
     */
    protected $table = 'organization_images';

    /**
     * @var string The primary key for the table.
     */
    protected $primaryKey = 'id';

    /**
     * @var bool Whether the model should use auto-incrementing primary keys.
     */
    protected $useAutoIncrement = true;

    /**
     * @var string The type of result to return. 'array' or 'object'.
     */
    protected $returnType = 'array';

    /**
     * @var bool Whether the model should use soft deletes.
     */
    protected $useSoftDeletes = true;

    /**
     * @var array The fields that can be mass-assigned.
     * Includes all image, organization, and audit fields.
     */
    protected $allowedFields = [
        'organization_id', 'image_path', 'caption', 'sort_order',
        'created_by', 'updated_by', 'deleted_by',
    ];

    /**
     * @var bool Whether the model should use timestamps.
     */
    protected $useTimestamps = true;

    /**
     * @var string The name of the `created_at` column.
     */
    protected $createdField = 'created_at';

    /**
     * @var string The name of the `updated_at` column.
     * Note: This field is not present in the table, but included for consistency.
     */
    protected $updatedField = 'updated_at';

    /**
     * @var string The name of the `deleted_at` column.
     */
    protected $deletedField = 'deleted_at';

    /**
     * @var array Validation rules for the model's fields.
     * Enforces required fields, correct formats, and audit columns.
     */
    protected $validationRules = [
        'organization_id' => 'required',
        'image_path'      => 'required',
    ];

    /**
     * @var array Custom error messages for validation rules.
     */
    protected $validationMessages = [];

    /**
     * @var bool Whether to skip validation on insert/update.
     */
    protected $skipValidation = false;

    /**
     * @var bool Whether to clean validation rules before validation.
     */
    protected $cleanValidationRules = true;

    /**
     * @var bool Whether to allow callbacks.
     */
    protected $allowCallbacks = true;
    protected $beforeInsert   = [];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];
}