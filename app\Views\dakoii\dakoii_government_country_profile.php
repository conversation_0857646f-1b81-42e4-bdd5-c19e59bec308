<?= $this->extend('templates/dakoii_portal_template') ?>
<?= $this->section('content') ?>
<h1>Country Profile</h1>
<?php if (isset($country)): ?>
    <ul>
        <li><strong>Name:</strong> <?= esc($country['name']) ?></li>
        <li><strong>ISO2:</strong> <?= esc($country['iso2']) ?></li>
        <li><strong>ISO3:</strong> <?= esc($country['iso3']) ?></li>
    </ul>
    <a class="btn btn-secondary" href="<?= base_url('dakoii/government/countries/'.$country['id'].'/edit') ?>">Edit</a>
    <form action="<?= base_url('dakoii/government/countries/'.$country['id'].'/delete') ?>" method="post" style="display:inline;">
        <?= csrf_field() ?>
        <button type="submit" onclick="return confirm('Delete this country?')">Delete</button>
    </form>
<?php else: ?>
    <p>Country not found.</p>
<?php endif; ?>
<?= $this->endSection() ?> 